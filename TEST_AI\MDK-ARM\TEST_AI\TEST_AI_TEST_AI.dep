Dependencies for Project 'TEST_AI', Target 'TEST_AI': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (startup_stm32f407xx.s)(0x00000000)(--cpu Cortex-M4 -g --apcs=interwork 

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

--pd "__UVISION_VERSION SETA 541" --pd "STM32F407xx SETA 1"

--list .\test_ai\startup_stm32f407xx.lst --xref -o .\test_ai\startup_stm32f407xx.o --depend .\test_ai\startup_stm32f407xx.d)
F (../Core/Src/main.c)(0x6875B703)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\main.o --omf_browse .\test_ai\main.crf --depend .\test_ai\main.d)
I (..\Core\Inc\main.h)(0x6875B75B)
F (../Core/Src/gpio.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\gpio.o --omf_browse .\test_ai\gpio.crf --depend .\test_ai\gpio.d)
F (../Core/Src/uart_comm.c)(0x6875B65E)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\uart_comm.o --omf_browse .\test_ai\uart_comm.crf --depend .\test_ai\uart_comm.d)
I (..\Core\Inc\uart_comm.h)(0x6875B636)
I (..\Core\Inc\main.h)(0x6875B75B)
F (../Core/Src/data_process.c)(0x6875B69F)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\data_process.o --omf_browse .\test_ai\data_process.crf --depend .\test_ai\data_process.d)
I (..\Core\Inc\data_process.h)(0x6875B677)
I (..\Core\Inc\main.h)(0x6875B75B)
F (../Core/Src/key_handler.c)(0x6875B6DE)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\key_handler.o --omf_browse .\test_ai\key_handler.crf --depend .\test_ai\key_handler.d)
I (..\Core\Inc\key_handler.h)(0x6875B6B5)
I (..\Core\Inc\main.h)(0x6875B75B)
F (../Core/Src/dma.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\dma.o --omf_browse .\test_ai\dma.crf --depend .\test_ai\dma.d)
F (../Core/Src/i2c.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\i2c.o --omf_browse .\test_ai\i2c.crf --depend .\test_ai\i2c.d)
F (../Core/Src/spi.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\spi.o --omf_browse .\test_ai\spi.crf --depend .\test_ai\spi.d)
F (../Core/Src/tim.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\tim.o --omf_browse .\test_ai\tim.crf --depend .\test_ai\tim.d)
F (../Core/Src/usart.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\usart.o --omf_browse .\test_ai\usart.crf --depend .\test_ai\usart.d)
F (../Core/Src/stm32f4xx_it.c)(0x6875B74C)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_it.o --omf_browse .\test_ai\stm32f4xx_it.crf --depend .\test_ai\stm32f4xx_it.d)
I (..\Core\Inc\main.h)(0x6875B75B)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_msp.o --omf_browse .\test_ai\stm32f4xx_hal_msp.crf --depend .\test_ai\stm32f4xx_hal_msp.d)
F (../Core/Src/system_stm32f4xx.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\system_stm32f4xx.o --omf_browse .\test_ai\system_stm32f4xx.crf --depend .\test_ai\system_stm32f4xx.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_gpio.o --omf_browse .\test_ai\stm32f4xx_hal_gpio.crf --depend .\test_ai\stm32f4xx_hal_gpio.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_dma_ex.o --omf_browse .\test_ai\stm32f4xx_hal_dma_ex.crf --depend .\test_ai\stm32f4xx_hal_dma_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_dma.o --omf_browse .\test_ai\stm32f4xx_hal_dma.crf --depend .\test_ai\stm32f4xx_hal_dma.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_pwr.o --omf_browse .\test_ai\stm32f4xx_hal_pwr.crf --depend .\test_ai\stm32f4xx_hal_pwr.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_pwr_ex.o --omf_browse .\test_ai\stm32f4xx_hal_pwr_ex.crf --depend .\test_ai\stm32f4xx_hal_pwr_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_cortex.o --omf_browse .\test_ai\stm32f4xx_hal_cortex.crf --depend .\test_ai\stm32f4xx_hal_cortex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal.o --omf_browse .\test_ai\stm32f4xx_hal.crf --depend .\test_ai\stm32f4xx_hal.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_exti.o --omf_browse .\test_ai\stm32f4xx_hal_exti.crf --depend .\test_ai\stm32f4xx_hal_exti.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_rcc.o --omf_browse .\test_ai\stm32f4xx_hal_rcc.crf --depend .\test_ai\stm32f4xx_hal_rcc.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_rcc_ex.o --omf_browse .\test_ai\stm32f4xx_hal_rcc_ex.crf --depend .\test_ai\stm32f4xx_hal_rcc_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_flash.o --omf_browse .\test_ai\stm32f4xx_hal_flash.crf --depend .\test_ai\stm32f4xx_hal_flash.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_flash_ex.o --omf_browse .\test_ai\stm32f4xx_hal_flash_ex.crf --depend .\test_ai\stm32f4xx_hal_flash_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_flash_ramfunc.o --omf_browse .\test_ai\stm32f4xx_hal_flash_ramfunc.crf --depend .\test_ai\stm32f4xx_hal_flash_ramfunc.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_i2c.o --omf_browse .\test_ai\stm32f4xx_hal_i2c.crf --depend .\test_ai\stm32f4xx_hal_i2c.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_i2c_ex.o --omf_browse .\test_ai\stm32f4xx_hal_i2c_ex.crf --depend .\test_ai\stm32f4xx_hal_i2c_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_spi.o --omf_browse .\test_ai\stm32f4xx_hal_spi.crf --depend .\test_ai\stm32f4xx_hal_spi.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_tim.o --omf_browse .\test_ai\stm32f4xx_hal_tim.crf --depend .\test_ai\stm32f4xx_hal_tim.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_tim_ex.o --omf_browse .\test_ai\stm32f4xx_hal_tim_ex.crf --depend .\test_ai\stm32f4xx_hal_tim_ex.d)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\stm32f4xx_hal_uart.o --omf_browse .\test_ai\stm32f4xx_hal_uart.crf --depend .\test_ai\stm32f4xx_hal_uart.d)
F (../Drivers/CMSIS/Device/ST/STM32F4xx/Source/Templates/system_stm32f4xx.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\system_stm32f4xx_1.o --omf_browse .\test_ai\system_stm32f4xx_1.crf --depend .\test_ai\system_stm32f4xx_1.d)
F (../Drivers/BSP/motor.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\motor.o --omf_browse .\test_ai\motor.crf --depend .\test_ai\motor.d)
F (../Drivers/BSP/encoder.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\encoder.o --omf_browse .\test_ai\encoder.crf --depend .\test_ai\encoder.d)
F (../Drivers/BSP/oled.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\oled.o --omf_browse .\test_ai\oled.crf --depend .\test_ai\oled.d)
F (../Drivers/BSP/pid.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\pid.o --omf_browse .\test_ai\pid.crf --depend .\test_ai\pid.d)
F (../Drivers/BSP/bsp_uart.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\bsp_uart.o --omf_browse .\test_ai\bsp_uart.crf --depend .\test_ai\bsp_uart.d)
F (../Drivers/BSP/mpu6050.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\mpu6050.o --omf_browse .\test_ai\mpu6050.crf --depend .\test_ai\mpu6050.d)
F (../Drivers/BSP/key.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\key.o --omf_browse .\test_ai\key.crf --depend .\test_ai\key.d)
F (../Drivers/BSP/control.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\control.o --omf_browse .\test_ai\control.crf --depend .\test_ai\control.d)
F (../Drivers/BSP/24l01.c)(0x00000000)(--c99 -c --cpu Cortex-M4 -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc -I ..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy -I ..\Drivers\CMSIS\Device\ST\STM32F4xx\Include -I ..\Drivers\CMSIS\Include -I ..\Drivers\BSP

-ID:\keil\ARM\Packs\Keil\STM32F4xx_DFP\3.0.0\Drivers\CMSIS\Device\ST\STM32F4xx\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DUSE_HAL_DRIVER -DSTM32F407xx

-o .\test_ai\24l01.o --omf_browse .\test_ai\24l01.crf --depend .\test_ai\24l01.d)
