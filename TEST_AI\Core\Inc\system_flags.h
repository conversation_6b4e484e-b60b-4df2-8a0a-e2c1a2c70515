/**
  ******************************************************************************
  * @file    system_flags.h
  * @brief   系统标志位定义文件
  ******************************************************************************
  */

#ifndef __SYSTEM_FLAGS_H
#define __SYSTEM_FLAGS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"

/* 系统标志位定义 */
extern volatile uint8_t uart_data_ready_flag;    // UART数据接收完成标志
extern volatile uint8_t system_10ms_flag;        // 10ms系统节拍标志
extern volatile uint8_t key_scan_flag;           // 按键扫描标志
extern volatile uint8_t servo_update_flag;       // 舵机更新标志

/* 通信相关标志 */
extern volatile uint8_t rec_flag;                // 接收标志
extern volatile uint8_t rec_ok_flag;             // 接收完成标志
extern volatile uint8_t rx_ack;                  // 接收应答标志
extern volatile uint8_t delay_flag;              // 延时标志
extern volatile uint8_t pause_flag;              // 暂停标志
extern volatile uint8_t reset_flag;              // 复位标志
extern volatile uint8_t point_state;             // 点状态

/* 任务相关变量 */
extern volatile uint8_t task;                    // 当前任务号

/* UART接收相关 */
extern uint8_t rx_len;                           // 接收数据长度
extern uint8_t RxBuffer[255];                    // 接收缓冲区

/* 控制相关变量 */
extern int Encoder_All_L;                        // 左编码器累计值
extern int Encoder_All_R;                        // 右编码器累计值
extern int dir_error;                            // 方向误差
extern float angle;                              // 角度
extern float servo_hori_error;                   // 水平舵机误差
extern float servo_vert_error;                   // 垂直舵机误差

extern float servo_hori_all;                     // 水平舵机累计值
extern float servo_vert_all;                     // 垂直舵机累计值
extern uint16_t servo_midx;                      // 舵机中位X
extern uint16_t servo_midy;                      // 舵机中位Y
extern uint16_t servo_savex;                     // 舵机保存X
extern uint16_t servo_savey;                     // 舵机保存Y

/* 函数声明 */
void SystemFlags_Init(void);
void SystemFlags_Clear(void);

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_FLAGS_H */
