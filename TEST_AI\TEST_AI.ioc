#MicroXplorer Configuration settings - do not modify
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SPI1
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM3
Mcu.IP8=TIM4
Mcu.IP9=TIM5
Mcu.IP10=TIM6
Mcu.IP11=TIM8
Mcu.IP12=USART1
Mcu.IP13=USART3
Mcu.IPNb=14
Mcu.Name=STM32F407VETx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin2=PE4
Mcu.Pin3=PE5
Mcu.Pin4=PE6
Mcu.Pin5=PC13-ANTI_TAMP
Mcu.Pin6=PC14-OSC32_IN
Mcu.Pin7=PC15-OSC32_OUT
Mcu.Pin8=PH0-OSC_IN
Mcu.Pin9=PH1-OSC_OUT
Mcu.Pin10=PA5
Mcu.Pin11=PA6
Mcu.Pin12=PA7
Mcu.Pin13=PC4
Mcu.Pin14=PC5
Mcu.Pin15=PB0
Mcu.Pin16=PB1
Mcu.Pin17=PE8
Mcu.Pin18=PE9
Mcu.Pin19=PE11
Mcu.Pin20=PE13
Mcu.Pin21=PE14
Mcu.Pin22=PB10
Mcu.Pin23=PB11
Mcu.Pin24=PB12
Mcu.Pin25=PB13
Mcu.Pin26=PB14
Mcu.Pin27=PB15
Mcu.Pin28=PA8
Mcu.Pin29=PA9
Mcu.Pin30=PA10
Mcu.Pin31=PA11
Mcu.Pin32=PA12
Mcu.Pin33=PA15
Mcu.Pin34=PC10
Mcu.Pin35=PC11
Mcu.Pin36=PD2
Mcu.Pin37=PB3
Mcu.Pin38=PB6
Mcu.Pin39=PB7
Mcu.Pin40=VP_SYS_VS_Systick
Mcu.Pin41=VP_TIM1_VS_ClockSourceINT
Mcu.Pin42=VP_TIM6_VS_ClockSourceINT
Mcu.PinsNb=43
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.8.1
MxDb.Version=DB.6.0.81
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_TIM10_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.TIM6_DAC_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Signal=USB_OTG_FS_DM
PA12.Signal=USB_OTG_FS_DP
PA15.Signal=S_TIM2_CH1_ETR
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA8.Signal=S_TIM1_CH1
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Signal=S_TIM3_CH3
PB1.Signal=S_TIM3_CH4
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Signal=S_TIM1_BKIN
PB13.Signal=S_TIM1_CH1N
PB14.Signal=S_TIM1_CH2N
PB15.Signal=S_TIM1_CH3N
PB3.Signal=S_TIM2_CH2
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC10.Signal=S_TIM3_CH1
PC11.Signal=S_TIM3_CH2
PC13-ANTI_TAMP.GPIOParameters=GPIO_Label
PC13-ANTI_TAMP.GPIO_Label=LED1
PC13-ANTI_TAMP.Locked=true
PC13-ANTI_TAMP.Signal=GPIO_Output
PC14-OSC32_IN.GPIOParameters=GPIO_Label
PC14-OSC32_IN.GPIO_Label=LED2
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Signal=GPIO_Output
PC15-OSC32_OUT.GPIOParameters=GPIO_Label
PC15-OSC32_OUT.GPIO_Label=LED3
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Signal=GPIO_Output
PC4.Signal=S_TIM8_CH4
PC5.Signal=S_TIM8_CH1
PD2.Signal=S_TIM3_ETR
PE11.Signal=S_TIM1_CH2
PE13.Signal=S_TIM1_CH3
PE14.Signal=S_TIM1_CH4
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=LASER
PE2.Locked=true
PE2.Signal=GPIO_Output
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=KEY1
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_Label
PE4.GPIO_Label=KEY2
PE4.Locked=true
PE4.Signal=GPIO_Input
PE5.GPIOParameters=GPIO_Label
PE5.GPIO_Label=KEY3
PE5.Locked=true
PE5.Signal=GPIO_Input
PE6.GPIOParameters=GPIO_Label
PE6.GPIO_Label=KEY4
PE6.Locked=true
PE6.Signal=GPIO_Input
PE8.Signal=S_TIM1_CH1N
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.27.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=TEST_AI.ioc
ProjectManager.ProjectName=TEST_AI_UART_COMM
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_SPI1_Init-SPI1-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM4_Init-TIM4-false-HAL-true,9-MX_TIM5_Init-TIM5-false-HAL-true,10-MX_TIM6_Init-TIM6-false-HAL-true,11-MX_TIM8_Init-TIM8-false-HAL-true,12-MX_USART1_UART_Init-USART1-false-HAL-true,13-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH1N.0=TIM1_CH1N,PWM Generation1 CH1N
SH.S_TIM1_CH1N.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH2N.0=TIM1_CH2N,PWM Generation2 CH2N
SH.S_TIM1_CH2N.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM1_CH3N.0=TIM1_CH3N,PWM Generation3 CH3N
SH.S_TIM1_CH3N.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM4_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM5_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM5_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM3_CH3.0=TIM3_CH3,PWM Generation3 CH3
SH.S_TIM3_CH3.ConfNb=1
SH.S_TIM3_CH4.0=TIM3_CH4,PWM Generation4 CH4
SH.S_TIM3_CH4.ConfNb=1
SH.S_TIM3_ETR.0=TIM3_ETR,ClockSourceETR
SH.S_TIM3_ETR.ConfNb=1
SH.S_TIM8_CH1.0=TIM8_CH1,PWM Generation1 CH1
SH.S_TIM8_CH1.ConfNb=1
SH.S_TIM8_CH4.0=TIM8_CH4,PWM Generation4 CH4
SH.S_TIM8_CH4.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI1.CalculateBaudRate=42.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation1\ CH1N=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation2\ CH2N=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation3\ CH3N=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation1 CH1N,Channel-PWM Generation2 CH2,Channel-PWM Generation2 CH2N,Channel-PWM Generation3 CH3,Channel-PWM Generation3 CH3N,Channel-PWM Generation4 CH4
TIM3.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM3.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM3.IPParameters=Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4,Prescaler,Period
TIM3.Period=19999
TIM3.Prescaler=83
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IC1Filter=10
TIM4.IC2Filter=10
TIM4.IPParameters=EncoderMode,IC1Filter,IC2Filter
TIM5.EncoderMode=TIM_ENCODERMODE_TI12
TIM5.IC1Filter=10
TIM5.IC2Filter=10
TIM5.IPParameters=EncoderMode,IC1Filter,IC2Filter
TIM6.IPParameters=Prescaler,Period
TIM6.Period=999
TIM6.Prescaler=839
TIM8.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM8.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM8.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation4 CH4
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
isbadioc=false
