/**
  ******************************************************************************
  * @file    uart_protocol.h
  * @brief   UART通信协议模块头文件
  ******************************************************************************
  */

#ifndef __UART_PROTOCOL_H
#define __UART_PROTOCOL_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "usart.h"

/* 协议常量定义 */
#define PROTOCOL_FRAME_HEADER       0xA1    // 帧头标识
#define PROTOCOL_FRAME_TAIL         0x1A    // 帧尾标识
#define PROTOCOL_MAX_DATA_LEN       16      // 最大数据长度
#define PROTOCOL_MIN_PACKET_LEN     3       // 最小数据包长度

/* 命令字定义 */
#define CMD_SERVO_Y_POS             0x12    // Y轴舵机位置命令
#define CMD_SERVO_X_POS             0x13    // X轴舵机位置命令
#define CMD_COORD_ERROR             0x02    // 坐标误差数据
#define CMD_TASK_COMPLETE           0x11    // 任务完成命令
#define CMD_ACK_RESPONSE            0x33    // 应答命令
#define CMD_POINT_UPDATE            0x44    // 点位更新命令

/* 任务号定义 */
#define TASK_IDLE                   0       // 空闲任务
#define TASK_POINT_CONTROL          1       // 点位控制任务
#define TASK_TRACK_CONTROL          2       // 轨迹控制任务
#define TASK_RECT_TRACK             3       // 矩形轨迹任务
#define TASK_CIRCLE_TRACK           4       // 圆形轨迹任务
#define TASK_POINT_RECORD           9       // 点位记录任务

/* 协议状态定义 */
typedef enum {
    PROTOCOL_STATE_IDLE = 0,        // 空闲状态
    PROTOCOL_STATE_RECEIVING,       // 接收状态
    PROTOCOL_STATE_PROCESSING,      // 处理状态
    PROTOCOL_STATE_ERROR            // 错误状态
} protocol_state_t;

/* 数据包结构定义 */
typedef struct {
    uint8_t header;                 // 帧头
    uint8_t cmd;                   // 命令字
    uint8_t data[PROTOCOL_MAX_DATA_LEN]; // 数据域
    uint8_t data_len;              // 数据长度
    uint8_t tail;                  // 帧尾
    uint8_t checksum;              // 校验和（可选）
} protocol_packet_t;

/* 坐标误差数据结构 */
typedef struct {
    uint8_t hori_dir;              // 水平方向标志 (0=正, 1=负)
    uint8_t hori_value;            // 水平误差值
    uint8_t vert_dir;              // 垂直方向标志 (0=正, 1=负)
    uint8_t vert_value;            // 垂直误差值
} coord_error_data_t;

/* 舵机位置数据结构 */
typedef struct {
    uint16_t servo_x_pos;          // X轴舵机位置
    uint16_t servo_y_pos;          // Y轴舵机位置
} servo_position_t;

/* 通信统计信息 */
typedef struct {
    uint32_t total_received;       // 总接收包数
    uint32_t total_sent;           // 总发送包数
    uint32_t error_packets;        // 错误包数
    uint32_t checksum_errors;      // 校验错误数
    uint32_t timeout_errors;       // 超时错误数
} protocol_stats_t;

/* 全局变量声明 */
extern volatile uint8_t g_uart_rx_flag;           // UART接收标志
extern volatile uint16_t g_uart_rx_length;        // 接收数据长度
extern uint8_t g_uart_rx_buffer[UART_BUFFER_SIZE]; // 接收缓冲区
extern uint8_t g_uart_tx_buffer[64];              // 发送缓冲区

extern volatile uint8_t g_task_number;            // 当前任务号
extern volatile uint8_t g_ack_received;           // 应答接收标志
extern volatile uint8_t g_task_complete;          // 任务完成标志

extern protocol_stats_t g_protocol_stats;         // 协议统计信息

/* 函数声明 */

/**
 * @brief  UART协议模块初始化
 * @param  None
 * @retval None
 */
void UART_Protocol_Init(void);

/**
 * @brief  UART协议处理主函数
 * @param  None
 * @retval None
 */
void UART_Protocol_Process(void);

/**
 * @brief  解析接收到的数据包
 * @param  buffer: 数据缓冲区指针
 * @param  length: 数据长度
 * @retval 解析结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Parse(uint8_t *buffer, uint16_t length);

/**
 * @brief  发送任务号
 * @param  task_num: 任务号
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Task(uint8_t task_num);

/**
 * @brief  发送应答信号
 * @param  None
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_ACK(void);

/**
 * @brief  发送任务完成信号
 * @param  None
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Complete(void);

/**
 * @brief  发送自定义数据包
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Packet(uint8_t cmd, uint8_t *data, uint8_t data_len);

/**
 * @brief  处理舵机位置命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Servo_Cmd(uint8_t cmd, uint8_t *data);

/**
 * @brief  处理坐标误差数据
 * @param  data: 数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Coord_Error(uint8_t *data);

/**
 * @brief  处理控制命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Control_Cmd(uint8_t cmd, uint8_t *data, uint8_t data_len);

/**
 * @brief  UART空闲中断回调函数
 * @param  huart: UART句柄指针
 * @retval None
 */
void UART_Protocol_IDLE_Callback(UART_HandleTypeDef *huart);

/**
 * @brief  获取协议状态
 * @param  None
 * @retval 协议状态
 */
protocol_state_t UART_Protocol_Get_State(void);

/**
 * @brief  重置协议状态
 * @param  None
 * @retval None
 */
void UART_Protocol_Reset(void);

/**
 * @brief  获取协议统计信息
 * @param  None
 * @retval 统计信息指针
 */
protocol_stats_t* UART_Protocol_Get_Stats(void);

/**
 * @brief  计算校验和
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval 校验和
 */
uint8_t UART_Protocol_Calculate_Checksum(uint8_t *data, uint8_t length);

/**
 * @brief  验证数据包完整性
 * @param  packet: 数据包指针
 * @retval 验证结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Verify_Packet(protocol_packet_t *packet);

#ifdef __cplusplus
}
#endif

#endif /* __UART_PROTOCOL_H */
