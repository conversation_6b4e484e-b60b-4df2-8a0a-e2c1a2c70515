/**
 ******************************************************************************
 * @file    system_manager.c
 * @brief   系统管理模块实现
 ******************************************************************************
 */

#include "system_manager.h"
#include "uart_protocol.h"
#include "data_handler.h"
#include <string.h>
#include <stdio.h>

/* 全局变量定义 */
volatile system_status_t g_system_status = {0};
volatile uint32_t g_system_events = 0;
performance_monitor_t g_performance_monitor = {0};

/* 内部变量 */
static system_task_t system_tasks[8];    // 系统任务数组
static uint8_t task_count = 0;           // 任务数量
static uint32_t last_heartbeat_time = 0; // 上次心跳时间
static uint32_t last_status_update = 0;  // 上次状态更新时间

/**
 * @brief  系统管理模块初始化
 * @param  None
 * @retval None
 */
void System_Manager_Init(void)
{
    /* 初始化系统状态 */
    g_system_status.state = SYS_STATE_INIT;
    g_system_status.uptime = 0;
    g_system_status.last_heartbeat = HAL_GetTick();
    g_system_status.error_code = ERROR_NONE;
    g_system_status.reset_count = 0;
    g_system_status.cpu_usage = 0;
    g_system_status.free_memory = 0;

    /* 清除事件标志 */
    g_system_events = 0;

    /* 初始化性能监控 */
    memset(&g_performance_monitor, 0, sizeof(performance_monitor_t));

    /* 初始化任务数组 */
    memset(system_tasks, 0, sizeof(system_tasks));
    task_count = 0;

    /* 记录初始化时间 */
    last_heartbeat_time = HAL_GetTick();
    last_status_update = HAL_GetTick();

    printf("System Manager Initialized\r\n");
}

/**
 * @brief  系统管理主处理函数
 * @param  None
 * @retval None
 */
void System_Manager_Process(void)
{
    uint32_t start_time = HAL_GetTick();

    /* 更新系统状态 */
    System_Manager_Update_Status();

    /* 处理系统事件 */
    System_Manager_Handle_Events();

    /* 任务调度 */
    System_Manager_Task_Scheduler();

    /* 性能监控 */
    System_Manager_Performance_Monitor();

    /* 看门狗喂狗 */
    System_Manager_Feed_Watchdog();

    /* 更新性能统计 */
    uint32_t execution_time = HAL_GetTick() - start_time;
    g_performance_monitor.task_run_count++;
    g_performance_monitor.total_execution_time += execution_time;

    if (execution_time > g_performance_monitor.max_execution_time)
    {
        g_performance_monitor.max_execution_time = execution_time;
    }

    g_performance_monitor.avg_execution_time =
        g_performance_monitor.total_execution_time / g_performance_monitor.task_run_count;
}

/**
 * @brief  系统状态更新
 * @param  None
 * @retval None
 */
void System_Manager_Update_Status(void)
{
    uint32_t current_time = HAL_GetTick();

    /* 每100ms更新一次状态 */
    if ((current_time - last_status_update) >= STATUS_UPDATE_INTERVAL)
    {
        last_status_update = current_time;

        /* 更新运行时间 */
        g_system_status.uptime = current_time;

        /* 更新CPU使用率 */
        g_system_status.cpu_usage = System_Manager_Get_CPU_Usage();

        /* 更新剩余内存 */
        g_system_status.free_memory = System_Manager_Get_Free_Memory();

        /* 检查系统健康状态 */
        if (g_system_status.error_code == ERROR_NONE)
        {
            if (g_system_status.state == SYS_STATE_INIT)
            {
                g_system_status.state = SYS_STATE_READY;
            }
            else if (g_system_status.state == SYS_STATE_READY)
            {
                g_system_status.state = SYS_STATE_RUNNING;
            }
        }
    }
}

/**
 * @brief  发送系统事件
 * @param  event_id: 事件ID
 * @param  priority: 优先级
 * @param  data: 事件数据指针
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t System_Manager_Send_Event(uint8_t event_id, uint8_t priority, void *data)
{
    /* 设置事件标志 */
    g_system_events |= (1 << event_id);

    printf("Event Sent: ID=%d, Priority=%d\r\n", event_id, priority);

    return 0;
}

/**
 * @brief  处理系统事件
 * @param  None
 * @retval None
 */
void System_Manager_Handle_Events(void)
{
    /* 检查UART数据接收事件 */
    if (g_system_events & (1 << 0))
    {
        g_system_events &= ~(1 << 0);
        /* UART数据接收事件已在协议模块中处理 */
    }

    /* 检查定时器超时事件 */
    if (g_system_events & (1 << 1))
    {
        g_system_events &= ~(1 << 1);
        printf("Timer Expired Event\r\n");
    }

    /* 检查任务完成事件 */
    if (g_system_events & (1 << 2))
    {
        g_system_events &= ~(1 << 2);
        printf("Task Complete Event\r\n");
    }

    /* 检查错误发生事件 */
    if (g_system_events & (1 << 3))
    {
        g_system_events &= ~(1 << 3);
        printf("Error Occurred Event\r\n");
    }

    /* 检查系统复位事件 */
    if (g_system_events & (1 << 4))
    {
        g_system_events &= ~(1 << 4);
        printf("System Reset Event\r\n");
        System_Manager_Reset(0); // 软复位
    }
}

/**
 * @brief  注册系统任务
 * @param  task_func: 任务函数指针
 * @param  period: 执行周期 (ms)
 * @param  priority: 优先级
 * @retval 注册结果 (0=成功, 其他=错误码)
 */
uint8_t System_Manager_Register_Task(void (*task_func)(void), uint32_t period, uint8_t priority)
{
    if (task_count >= 8 || !task_func)
    {
        return 1; // 任务数量超限或函数指针无效
    }

    system_tasks[task_count].task_func = task_func;
    system_tasks[task_count].period = period;
    system_tasks[task_count].last_run = HAL_GetTick();
    system_tasks[task_count].priority = priority;
    system_tasks[task_count].enabled = 1;

    task_count++;

    printf("Task Registered: Count=%d, Period=%ld, Priority=%d\r\n",
           task_count, period, priority);

    return 0;
}

/**
 * @brief  任务调度器
 * @param  None
 * @retval None
 */
void System_Manager_Task_Scheduler(void)
{
    uint32_t current_time = HAL_GetTick();

    for (uint8_t i = 0; i < task_count; i++)
    {
        if (system_tasks[i].enabled && system_tasks[i].task_func)
        {
            /* 检查是否到达执行时间 */
            if ((current_time - system_tasks[i].last_run) >= system_tasks[i].period)
            {
                system_tasks[i].last_run = current_time;

                /* 执行任务 */
                system_tasks[i].task_func();
            }
        }
    }
}

/**
 * @brief  系统心跳处理
 * @param  None
 * @retval None
 */
void System_Manager_Heartbeat(void)
{
    uint32_t current_time = HAL_GetTick();

    /* 每秒更新一次心跳 */
    if ((current_time - last_heartbeat_time) >= HEARTBEAT_INTERVAL)
    {
        last_heartbeat_time = current_time;
        g_system_status.last_heartbeat = current_time;

        /* 输出系统状态信息 */
        printf("Heartbeat: Uptime=%lds, CPU=%d%%, Memory=%d bytes\r\n",
               g_system_status.uptime / 1000,
               g_system_status.cpu_usage,
               g_system_status.free_memory);
    }
}

/**
 * @brief  错误处理
 * @param  error_code: 错误代码
 * @retval None
 */
void System_Manager_Handle_Error(uint8_t error_code)
{
    g_system_status.error_code = error_code;
    g_system_status.state = SYS_STATE_ERROR;

    printf("Error Handled: Code=%d\r\n", error_code);

    /* 根据错误类型采取相应措施 */
    switch (error_code)
    {
    case ERROR_UART_TIMEOUT:
        /* UART超时错误：重新初始化UART */
        UART_Protocol_Reset();
        break;

    case ERROR_DATA_INVALID:
        /* 数据无效错误：重置数据处理模块 */
        Data_Handler_Reset();
        break;

    case ERROR_BUFFER_OVERFLOW:
        /* 缓冲区溢出：清除缓冲区 */
        break;

    case ERROR_SYSTEM_FAULT:
        /* 系统故障：执行系统复位 */
        System_Manager_Reset(1);
        break;

    default:
        break;
    }

    /* 清除错误状态 */
    HAL_Delay(100);
    g_system_status.error_code = ERROR_NONE;
    g_system_status.state = SYS_STATE_RUNNING;
}

/**
 * @brief  系统复位
 * @param  reset_type: 复位类型 (0=软复位, 1=硬复位)
 * @retval None
 */
void System_Manager_Reset(uint8_t reset_type)
{
    printf("System Reset: Type=%d\r\n", reset_type);

    g_system_status.reset_count++;

    if (reset_type == 0)
    {
        /* 软复位：重新初始化模块 */
        UART_Protocol_Init();
        Data_Handler_Init();
        System_Manager_Init();
    }
    else
    {
        /* 硬复位：重启系统 */
        HAL_NVIC_SystemReset();
    }
}

/**
 * @brief  获取系统状态
 * @param  None
 * @retval 系统状态指针
 */
system_status_t *System_Manager_Get_Status(void)
{
    return (system_status_t *)&g_system_status;
}

/**
 * @brief  设置系统状态
 * @param  state: 系统状态
 * @retval None
 */
void System_Manager_Set_State(system_state_t state)
{
    g_system_status.state = state;
    printf("System State Set: %d\r\n", state);
}

/**
 * @brief  系统自检
 * @param  None
 * @retval 自检结果 (0=通过, 其他=错误码)
 */
uint8_t System_Manager_Self_Test(void)
{
    printf("System Self Test Started\r\n");

    /* 检查UART通信 */
    if (UART_Protocol_Get_State() == PROTOCOL_STATE_ERROR)
    {
        printf("UART Test Failed\r\n");
        return 1;
    }

    /* 检查数据处理模块 */
    if (Data_Handler_Get_Task_State() == DATA_STATE_ERROR)
    {
        printf("Data Handler Test Failed\r\n");
        return 2;
    }

    /* 检查内存 */
    if (System_Manager_Get_Free_Memory() < 100)
    {
        printf("Memory Test Failed\r\n");
        return 3;
    }

    printf("System Self Test Passed\r\n");
    return 0;
}

/**
 * @brief  性能监控
 * @param  None
 * @retval None
 */
void System_Manager_Performance_Monitor(void)
{
    static uint32_t last_monitor_time = 0;
    uint32_t current_time = HAL_GetTick();

    /* 每5秒输出一次性能信息 */
    if ((current_time - last_monitor_time) >= 5000)
    {
        last_monitor_time = current_time;

        printf("Performance: Runs=%ld, Max=%ldms, Avg=%ldms\r\n",
               g_performance_monitor.task_run_count,
               g_performance_monitor.max_execution_time,
               g_performance_monitor.avg_execution_time);

        /* 输出协议统计 */
        protocol_stats_t *protocol_stats = UART_Protocol_Get_Stats();
        printf("Protocol: RX=%ld, TX=%ld, Errors=%ld\r\n",
               protocol_stats->total_received,
               protocol_stats->total_sent,
               protocol_stats->error_packets);

        /* 输出数据处理统计 */
        data_stats_t *data_stats = Data_Handler_Get_Stats();
        printf("Data: Total=%ld, Coord=%ld, Servo=%ld\r\n",
               data_stats->total_processed,
               data_stats->coord_errors_processed,
               data_stats->servo_commands_processed);
    }
}

/**
 * @brief  内存使用监控
 * @param  None
 * @retval 剩余内存大小 (bytes)
 */
uint16_t System_Manager_Get_Free_Memory(void)
{
    /* 简单的内存估算 */
    extern uint32_t _end;
    extern uint32_t _estack;

    uint32_t stack_ptr;
    __asm volatile("mov %0, sp" : "=r"(stack_ptr));

    uint32_t heap_end = (uint32_t)&_end;
    uint32_t free_memory = stack_ptr - heap_end;

    return (uint16_t)(free_memory > 65535 ? 65535 : free_memory);
}

/**
 * @brief  CPU使用率计算
 * @param  None
 * @retval CPU使用率 (%)
 */
uint8_t System_Manager_Get_CPU_Usage(void)
{
    static uint32_t last_idle_time = 0;
    static uint32_t last_total_time = 0;

    uint32_t current_time = HAL_GetTick();
    uint32_t total_time = current_time - last_total_time;

    /* 简单的CPU使用率估算 */
    uint32_t busy_time = g_performance_monitor.total_execution_time - last_idle_time;

    uint8_t cpu_usage = 0;
    if (total_time > 0)
    {
        cpu_usage = (uint8_t)((busy_time * 100) / total_time);
        if (cpu_usage > 100)
            cpu_usage = 100;
    }

    last_idle_time = g_performance_monitor.total_execution_time;
    last_total_time = current_time;

    return cpu_usage;
}

/**
 * @brief  看门狗喂狗
 * @param  None
 * @retval None
 */
void System_Manager_Feed_Watchdog(void)
{
    /* 如果启用了硬件看门狗，在这里喂狗 */
    /* HAL_IWDG_Refresh(&hiwdg); */
}

/**
 * @brief  系统时间获取
 * @param  None
 * @retval 系统时间 (ms)
 */
uint32_t System_Manager_Get_Tick(void)
{
    return HAL_GetTick();
}

/**
 * @brief  延时函数
 * @param  delay_ms: 延时时间 (ms)
 * @retval None
 */
void System_Manager_Delay(uint32_t delay_ms)
{
    HAL_Delay(delay_ms);
}

/**
 * @brief  进入低功耗模式
 * @param  mode: 低功耗模式类型
 * @retval None
 */
void System_Manager_Enter_Low_Power(uint8_t mode)
{
    printf("Entering Low Power Mode: %d\r\n", mode);

    switch (mode)
    {
    case 0: // Sleep模式
        HAL_PWR_EnterSLEEPMode(PWR_MAINREGULATOR_ON, PWR_SLEEPENTRY_WFI);
        break;
    case 1: // Stop模式
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        break;
    case 2: // Standby模式
        HAL_PWR_EnterSTANDBYMode();
        break;
    default:
        break;
    }
}

/**
 * @brief  退出低功耗模式
 * @param  None
 * @retval None
 */
void System_Manager_Exit_Low_Power(void)
{
    printf("Exiting Low Power Mode\r\n");

    /* 重新配置系统时钟 */
    SystemClock_Config();

    /* 重新初始化外设 */
    UART_Protocol_Init();
}
