# UART_AI项目使用指南

## 项目概述

UART_AI是一个专门针对UART通信和数据处理的STM32项目，采用裸机架构，使用UART空闲中断+DMA方式实现高效的变长数据包接收和处理。

## 项目特点

### 核心功能
- **UART通信**: 与OpenMV的高效串口通信
- **数据处理**: 完整的协议解析和数据处理
- **系统管理**: 模块化的系统状态管理
- **实时响应**: 裸机架构，响应速度快

### 技术特色
- **空闲中断+DMA**: 支持变长数据包接收
- **模块化设计**: 清晰的代码结构
- **高效处理**: 优化的数据处理流程
- **完善监控**: 系统状态和性能监控

## 快速开始

### 1. 环境准备
- **STM32CubeMX**: 6.8.1或更高版本
- **Keil MDK-ARM**: V5.32或更高版本
- **硬件**: STM32F407VETx开发板
- **调试器**: ST-Link V2或更高版本

### 2. 项目生成
```bash
# 1. 打开STM32CubeMX
# 2. 打开UART_AI.ioc文件
# 3. 生成代码 (选择MDK-ARM V5)
# 4. 打开Keil工程文件
```

### 3. 编译下载
```bash
# 1. 打开MDK-ARM/UART_AI.uvprojx
# 2. 编译项目 (F7)
# 3. 下载到开发板 (F8)
# 4. 开始调试 (Ctrl+F5)
```

## 硬件连接

### 主要接口
```
UART3通信:
  PB10 -> USART3_TX (连接OpenMV RX)
  PB11 -> USART3_RX (连接OpenMV TX)
  GND  -> GND (共地)

LED指示:
  PC13 -> LED1 (系统状态)
  PC14 -> LED2 (心跳指示)
  PC15 -> LED3 (错误指示)

按键输入:
  PE3  -> KEY1 (功能按键1)
  PE4  -> KEY2 (功能按键2)
  PE5  -> KEY3 (功能按键3)
  PE6  -> KEY4 (功能按键4)
```

### 电源连接
```
VCC -> 3.3V
GND -> GND
```

## 软件架构

### 模块结构
```
UART_AI/
├── Core/
│   ├── Inc/                    # 头文件
│   │   ├── main.h              # 主头文件
│   │   ├── uart_protocol.h     # UART协议模块
│   │   ├── data_handler.h      # 数据处理模块
│   │   └── system_manager.h    # 系统管理模块
│   └── Src/                    # 源文件
│       ├── main.c              # 主程序
│       ├── uart_protocol.c     # UART协议实现
│       ├── data_handler.c      # 数据处理实现
│       ├── system_manager.c    # 系统管理实现
│       └── stm32f4xx_it.c      # 中断处理
├── Drivers/                    # 驱动库
├── MDK-ARM/                    # Keil工程
└── 文档/                       # 项目文档
```

### 数据流程
```
OpenMV发送数据 -> UART3接收 -> DMA传输 -> 空闲中断 -> 
协议解析 -> 数据处理 -> 系统响应 -> 状态更新
```

## 核心功能说明

### 1. UART协议模块
```c
// 初始化UART协议
UART_Protocol_Init();

// 处理接收数据
UART_Protocol_Process();

// 发送数据包
UART_Protocol_Send_Packet(cmd, data, len);
```

### 2. 数据处理模块
```c
// 初始化数据处理
Data_Handler_Init();

// 处理坐标误差
Data_Handler_Process_Coord_Error(&error_data);

// 处理舵机位置
Data_Handler_Process_Servo_Position(&servo_data);
```

### 3. 系统管理模块
```c
// 初始化系统管理
System_Manager_Init();

// 系统主处理
System_Manager_Process();

// 错误处理
System_Manager_Handle_Error(error_code);
```

## 通信协议

### 数据包格式
```
标准格式: [0xA1][CMD][DATA...][0x1A]
简单格式: [CMD][DATA...]
```

### 命令定义
```c
#define CMD_SERVO_Y_POS     0x12    // Y轴舵机位置
#define CMD_SERVO_X_POS     0x13    // X轴舵机位置
#define CMD_COORD_ERROR     0x02    // 坐标误差数据
#define CMD_TASK_COMPLETE   0x11    // 任务完成
#define CMD_ACK_RESPONSE    0x33    // 应答信号
#define CMD_POINT_UPDATE    0x44    // 点位更新
```

### 数据示例
```c
// 坐标误差数据包
uint8_t coord_packet[] = {0xA1, 0x02, 0x01, 0x0A, 0x00, 0x14, 0x1A};
// 含义: 帧头, 坐标误差命令, X负方向, X误差10, Y正方向, Y误差20, 帧尾

// 舵机位置数据包
uint8_t servo_packet[] = {0x12, 0x05, 0xDC};
// 含义: Y轴舵机命令, 高字节0x05, 低字节0xDC (位置1500)
```

## 系统状态监控

### LED指示
- **LED1**: 系统状态指示
  - 常亮: 系统正常运行
  - 闪烁: 系统初始化
  - 熄灭: 系统错误
  
- **LED2**: 心跳指示
  - 1秒闪烁一次: 系统正常
  - 快速闪烁: 系统繁忙
  - 不闪烁: 系统停止

- **LED3**: 错误指示
  - 熄灭: 无错误
  - 常亮: 有错误发生
  - 闪烁: 错误恢复中

### 串口调试
```c
// 启用调试输出
printf("UART_AI System Started\r\n");
printf("Coord Error: H=%f, V=%f\r\n", hori_error, vert_error);
printf("Servo Position: X=%d, Y=%d\r\n", servo_x, servo_y);
```

## 性能监控

### 统计信息
```c
// 协议统计
protocol_stats_t *stats = UART_Protocol_Get_Stats();
printf("RX: %ld, TX: %ld, Errors: %ld\r\n", 
       stats->total_received, stats->total_sent, stats->error_packets);

// 数据处理统计
data_stats_t *data_stats = Data_Handler_Get_Stats();
printf("Processed: %ld, Coord: %ld, Servo: %ld\r\n",
       data_stats->total_processed, 
       data_stats->coord_errors_processed,
       data_stats->servo_commands_processed);
```

### 系统信息
```c
// 系统状态
system_status_t *status = System_Manager_Get_Status();
printf("Uptime: %lds, CPU: %d%%, Memory: %d bytes\r\n",
       status->uptime / 1000, status->cpu_usage, status->free_memory);
```

## 调试方法

### 1. 串口调试
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无

### 2. LED调试
- 观察LED状态判断系统运行情况
- LED2心跳正常表示系统运行正常

### 3. Keil调试
- 设置断点调试
- 观察变量值
- 查看内存使用情况

## 常见问题

### 1. 编译错误
**问题**: 找不到头文件
**解决**: 检查包含路径设置，确保所有路径正确

### 2. 通信异常
**问题**: 无法接收数据
**解决**: 检查UART配置和硬件连接

### 3. 系统卡死
**问题**: 程序运行异常
**解决**: 检查中断配置和栈大小设置

### 4. 数据丢失
**问题**: 接收数据不完整
**解决**: 增大缓冲区或优化处理速度

## 扩展开发

### 1. 添加新协议
```c
// 在uart_protocol.h中添加新命令
#define CMD_NEW_COMMAND     0x20

// 在uart_protocol.c中添加处理函数
uint8_t UART_Protocol_Handle_New_Command(uint8_t *data);
```

### 2. 增加新功能
```c
// 在data_handler.h中添加新功能
uint8_t Data_Handler_New_Function(void);

// 在data_handler.c中实现功能
uint8_t Data_Handler_New_Function(void) {
    // 功能实现
    return 0;
}
```

### 3. 系统优化
- 优化数据处理算法
- 增加缓存机制
- 实现数据压缩

## 版本信息

- **版本**: V1.0
- **日期**: 2024年
- **作者**: UART_AI开发团队
- **许可**: MIT License

## 技术支持

如有问题请参考：
1. 项目文档
2. 代码注释
3. 调试输出信息

项目地址: UART_AI/
文档地址: UART_AI/文档/
