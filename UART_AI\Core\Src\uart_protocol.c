/**
 ******************************************************************************
 * @file    uart_protocol.c
 * @brief   UART通信协议模块实现
 ******************************************************************************
 */

#include "uart_protocol.h"
#include "data_handler.h"
#include "system_manager.h"
#include <string.h>
#include <stdio.h>

/* 全局变量定义 */
volatile uint8_t g_uart_rx_flag = 0;        // UART接收标志
volatile uint16_t g_uart_rx_length = 0;     // 接收数据长度
uint8_t g_uart_rx_buffer[UART_BUFFER_SIZE]; // 接收缓冲区
uint8_t g_uart_tx_buffer[64];               // 发送缓冲区

volatile uint8_t g_task_number = 0;   // 当前任务号
volatile uint8_t g_ack_received = 0;  // 应答接收标志
volatile uint8_t g_task_complete = 0; // 任务完成标志

protocol_stats_t g_protocol_stats = {0}; // 协议统计信息

/* 内部变量 */
static protocol_state_t protocol_state = PROTOCOL_STATE_IDLE;
static uint32_t last_receive_time = 0;

/**
 * @brief  UART协议模块初始化
 * @param  None
 * @retval None
 */
void UART_Protocol_Init(void)
{
    /* 清除所有标志位 */
    g_uart_rx_flag = 0;
    g_uart_rx_length = 0;
    g_task_number = 0;
    g_ack_received = 0;
    g_task_complete = 0;

    /* 清除统计信息 */
    memset(&g_protocol_stats, 0, sizeof(protocol_stats_t));

    /* 设置协议状态 */
    protocol_state = PROTOCOL_STATE_IDLE;

    /* 启用UART空闲中断 */
    __HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE);

    /* 启动DMA接收 */
    HAL_UART_Receive_DMA(&huart3, g_uart_rx_buffer, UART_BUFFER_SIZE);

    printf("UART Protocol Initialized\r\n");
}

/**
 * @brief  UART协议处理主函数
 * @param  None
 * @retval None
 */
void UART_Protocol_Process(void)
{
    /* 检查是否有新数据 */
    if (g_uart_rx_flag)
    {
        g_uart_rx_flag = 0;
        protocol_state = PROTOCOL_STATE_PROCESSING;

        /* 解析数据包 */
        uint8_t result = UART_Protocol_Parse(g_uart_rx_buffer, g_uart_rx_length);

        if (result == 0)
        {
            /* 解析成功 */
            g_protocol_stats.total_received++;
            protocol_state = PROTOCOL_STATE_IDLE;
        }
        else
        {
            /* 解析失败 */
            g_protocol_stats.error_packets++;
            protocol_state = PROTOCOL_STATE_ERROR;
            System_Manager_Handle_Error(ERROR_DATA_INVALID);
        }

        /* 更新最后接收时间 */
        last_receive_time = HAL_GetTick();
    }

    /* 检查通信超时 */
    if ((HAL_GetTick() - last_receive_time) > 5000)
    {
        if (protocol_state != PROTOCOL_STATE_IDLE)
        {
            g_protocol_stats.timeout_errors++;
            System_Manager_Handle_Error(ERROR_UART_TIMEOUT);
            protocol_state = PROTOCOL_STATE_IDLE;
        }
    }
}

/**
 * @brief  解析接收到的数据包
 * @param  buffer: 数据缓冲区指针
 * @param  length: 数据长度
 * @retval 解析结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Parse(uint8_t *buffer, uint16_t length)
{
    if (length < 1)
        return 1; // 数据长度不足

    uint8_t cmd = buffer[0];

    /* 处理舵机位置命令 */
    if (cmd == CMD_SERVO_Y_POS || cmd == CMD_SERVO_X_POS)
    {
        if (length >= 3)
        {
            return UART_Protocol_Handle_Servo_Cmd(cmd, &buffer[1]);
        }
        return 2; // 数据长度不足
    }

    /* 处理控制命令 */
    if (cmd == PROTOCOL_FRAME_HEADER && length >= 3 &&
        buffer[length - 1] == PROTOCOL_FRAME_TAIL)
    {
        return UART_Protocol_Handle_Control_Cmd(buffer[1], &buffer[2], length - 3);
    }

    return 3; // 未知命令
}

/**
 * @brief  处理舵机位置命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Servo_Cmd(uint8_t cmd, uint8_t *data)
{
    uint16_t servo_value = (data[0] << 8) | data[1];
    servo_position_t servo_pos;

    if (cmd == CMD_SERVO_Y_POS)
    {
        servo_pos.servo_y_pos = servo_value;
        servo_pos.servo_x_pos = 0; // 不更新X轴
        printf("Servo Y Position: %d\r\n", servo_value);
    }
    else if (cmd == CMD_SERVO_X_POS)
    {
        servo_pos.servo_x_pos = servo_value;
        servo_pos.servo_y_pos = 0; // 不更新Y轴
        printf("Servo X Position: %d\r\n", servo_value);
    }

    /* 调用数据处理模块 */
    return Data_Handler_Process_Servo_Position(&servo_pos);
}

/**
 * @brief  处理控制命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Control_Cmd(uint8_t cmd, uint8_t *data, uint8_t data_len)
{
    switch (cmd)
    {
    case CMD_TASK_COMPLETE:
        g_task_complete = 1;
        printf("Task Complete Received\r\n");
        break;

    case CMD_ACK_RESPONSE:
        g_ack_received = 1;
        printf("ACK Received\r\n");
        break;

    case CMD_POINT_UPDATE:
        printf("Point Update Received\r\n");
        /* 可以在这里处理点位更新逻辑 */
        break;

    case CMD_COORD_ERROR:
        if (data_len >= 4)
        {
            return UART_Protocol_Handle_Coord_Error(data);
        }
        return 2; // 数据长度不足

    default:
        return 3; // 未知命令
    }

    return 0;
}

/**
 * @brief  处理坐标误差数据
 * @param  data: 数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Handle_Coord_Error(uint8_t *data)
{
    coord_error_data_t error_data;

    /* 解析坐标误差数据 */
    error_data.hori_dir = data[0];
    error_data.hori_value = data[1];
    error_data.vert_dir = data[2];
    error_data.vert_value = data[3];

    printf("Coord Error: H=%s%d, V=%s%d\r\n",
           error_data.hori_dir ? "-" : "+", error_data.hori_value,
           error_data.vert_dir ? "-" : "+", error_data.vert_value);

    /* 调用数据处理模块 */
    return Data_Handler_Process_Coord_Error(&error_data);
}

/**
 * @brief  发送任务号
 * @param  task_num: 任务号
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Task(uint8_t task_num)
{
    char task_str[2];

    /* 根据任务号映射发送的字符 */
    if (task_num == 5 || task_num == 7 || task_num == 9)
    {
        sprintf(task_str, "3");
    }
    else if (task_num == 6 || task_num == 8 || task_num == 10)
    {
        sprintf(task_str, "4");
    }
    else
    {
        sprintf(task_str, "%d", task_num);
    }

    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart3, (uint8_t *)task_str, 1, 100);

    if (status == HAL_OK)
    {
        g_protocol_stats.total_sent++;
        printf("Task %d Sent\r\n", task_num);
        return 0;
    }

    return 1;
}

/**
 * @brief  发送应答信号
 * @param  None
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_ACK(void)
{
    uint8_t ack_packet[] = {PROTOCOL_FRAME_HEADER, CMD_ACK_RESPONSE, PROTOCOL_FRAME_TAIL};

    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart3, ack_packet, sizeof(ack_packet), 100);

    if (status == HAL_OK)
    {
        g_protocol_stats.total_sent++;
        printf("ACK Sent\r\n");
        return 0;
    }

    return 1;
}

/**
 * @brief  发送任务完成信号
 * @param  None
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Complete(void)
{
    uint8_t complete_packet[] = {PROTOCOL_FRAME_HEADER, CMD_TASK_COMPLETE, PROTOCOL_FRAME_TAIL};

    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart3, complete_packet, sizeof(complete_packet), 100);

    if (status == HAL_OK)
    {
        g_protocol_stats.total_sent++;
        printf("Task Complete Sent\r\n");
        return 0;
    }

    return 1;
}

/**
 * @brief  UART空闲中断回调函数
 * @param  huart: UART句柄指针
 * @retval None
 */
void UART_Protocol_IDLE_Callback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART3)
    {
        /* 停止DMA传输 */
        HAL_UART_DMAStop(huart);

        /* 清除空闲中断标志 */
        __HAL_UART_CLEAR_IDLEFLAG(huart);

        /* 计算接收数据长度 */
        g_uart_rx_length = UART_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart3_rx);

        /* 重新启动DMA接收 */
        HAL_UART_Receive_DMA(huart, g_uart_rx_buffer, UART_BUFFER_SIZE);

        /* 设置数据接收标志 */
        g_uart_rx_flag = 1;
        protocol_state = PROTOCOL_STATE_RECEIVING;
    }
}

/**
 * @brief  获取协议状态
 * @param  None
 * @retval 协议状态
 */
protocol_state_t UART_Protocol_Get_State(void)
{
    return protocol_state;
}

/**
 * @brief  重置协议状态
 * @param  None
 * @retval None
 */
void UART_Protocol_Reset(void)
{
    protocol_state = PROTOCOL_STATE_IDLE;
    g_uart_rx_flag = 0;
    g_uart_rx_length = 0;
    g_ack_received = 0;
    g_task_complete = 0;
}

/**
 * @brief  获取协议统计信息
 * @param  None
 * @retval 统计信息指针
 */
protocol_stats_t *UART_Protocol_Get_Stats(void)
{
    return &g_protocol_stats;
}

/**
 * @brief  发送自定义数据包
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Send_Packet(uint8_t cmd, uint8_t *data, uint8_t data_len)
{
    if (data_len > PROTOCOL_MAX_DATA_LEN)
    {
        return 1; // 数据长度超限
    }

    uint8_t packet[PROTOCOL_MAX_DATA_LEN + 3];
    uint8_t packet_len = 0;

    /* 构造数据包 */
    packet[packet_len++] = PROTOCOL_FRAME_HEADER;
    packet[packet_len++] = cmd;

    if (data && data_len > 0)
    {
        memcpy(&packet[packet_len], data, data_len);
        packet_len += data_len;
    }

    packet[packet_len++] = PROTOCOL_FRAME_TAIL;

    /* 发送数据包 */
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart3, packet, packet_len, 100);

    if (status == HAL_OK)
    {
        g_protocol_stats.total_sent++;
        return 0;
    }

    return 2; // 发送失败
}

/**
 * @brief  计算校验和
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval 校验和
 */
uint8_t UART_Protocol_Calculate_Checksum(uint8_t *data, uint8_t length)
{
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++)
    {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * @brief  验证数据包完整性
 * @param  packet: 数据包指针
 * @retval 验证结果 (0=成功, 其他=错误码)
 */
uint8_t UART_Protocol_Verify_Packet(protocol_packet_t *packet)
{
    /* 检查帧头和帧尾 */
    if (packet->header != PROTOCOL_FRAME_HEADER)
    {
        return 1; // 帧头错误
    }

    if (packet->tail != PROTOCOL_FRAME_TAIL)
    {
        return 2; // 帧尾错误
    }

    /* 检查数据长度 */
    if (packet->data_len > PROTOCOL_MAX_DATA_LEN)
    {
        return 3; // 数据长度超限
    }

    return 0; // 验证通过
}
