# 运动控制系统移植详细步骤

## 第一步：复制原工程文件

### 1.1 复制BSP驱动文件
从原工程 `red/Drivers/BSP/` 复制以下文件到 `TEST_AI/Drivers/BSP/`：

```
- control.c/h
- pid.c/h  
- motor.c/h
- encoder.c/h
- mpu6050.c/h
- oled.c/h
- oledfont.h
- key.c/h
- bsp_uart.c/h
- 24l01.c/h
- common.h
- delay.h
```

### 1.2 复制HAL库文件
从原工程复制完整的HAL库：
```
red/Drivers/STM32F4xx_HAL_Driver/ → TEST_AI/Drivers/STM32F4xx_HAL_Driver/
red/Drivers/CMSIS/ → TEST_AI/Drivers/CMSIS/
```

### 1.3 复制启动文件
```
red/MDK-ARM/startup_stm32f407xx.s → TEST_AI/MDK-ARM/startup_stm32f407xx.s
```

## 第二步：使用CubeMX生成基础代码

### 2.1 打开CubeMX
1. 打开STM32CubeMX
2. 新建项目，选择STM32F407VETx
3. 导入配置文件 `TEST_AI.ioc`

### 2.2 配置外设
按照原工程配置：

**时钟配置：**
- HSE: 8MHz
- PLL: M=4, N=168, P=2
- SYSCLK: 168MHz
- APB1: 42MHz, APB2: 84MHz

**GPIO配置：**
- PE2: LASER (输出)
- PE3-PE6: KEY1-KEY4 (输入)
- PC13-PC15: LED1-LED3 (输出)

**定时器配置：**
- TIM3: PWM输出 (舵机控制)
  - CH3: PB0, CH4: PB1
  - 频率: 50Hz (周期20ms)
- TIM4: 编码器模式 (右轮)
- TIM5: 编码器模式 (左轮)  
- TIM6: 定时器中断 (10ms)
- TIM8: PWM输出 (电机控制)

**串口配置：**
- USART1: 调试串口 (PA9/PA10)
- USART3: 与OpenMV通信 (PB10/PB11)
  - 启用DMA接收
  - 启用空闲中断

**其他外设：**
- I2C1: MPU6050 (PB6/PB7)
- SPI1: OLED (PA5/PA6/PA7)

### 2.3 生成代码
1. 设置项目名称: TEST_AI
2. 设置IDE: MDK-ARM V5
3. 生成代码

## 第三步：修改生成的代码

### 3.1 替换main.c
用我们准备的 `TEST_AI/Core/Src/main.c` 替换生成的main.c

### 3.2 替换stm32f4xx_it.c  
用我们准备的 `TEST_AI/Core/Src/stm32f4xx_it.c` 替换生成的中断文件

### 3.3 添加系统管理文件
添加以下文件：
- `TEST_AI/Core/Inc/system_flags.h`
- `TEST_AI/Core/Src/system_flags.c`

### 3.4 修改main.h
在main.h中添加必要的声明和包含

## 第四步：配置Keil工程

### 4.1 打开工程
打开 `TEST_AI/MDK-ARM/TEST_AI.uvprojx`

### 4.2 添加包含路径
在C/C++选项中添加：
```
../Core/Inc
../Drivers/STM32F4xx_HAL_Driver/Inc
../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy
../Drivers/CMSIS/Device/ST/STM32F4xx/Include
../Drivers/CMSIS/Include
../Drivers/BSP
```

### 4.3 添加宏定义
```
USE_HAL_DRIVER
STM32F407xx
```

### 4.4 组织文件结构
按照以下分组组织文件：
- Application/MDK-ARM: 启动文件
- Application/User/Core: 核心应用文件
- Drivers/STM32F4xx_HAL_Driver: HAL库文件
- Drivers/CMSIS: CMSIS文件
- Drivers/BSP: BSP驱动文件

## 第五步：编译调试

### 5.1 首次编译
1. 编译工程，解决可能的错误
2. 检查包含路径和文件引用
3. 确保所有BSP文件正确包含

### 5.2 常见问题解决

**问题1：找不到头文件**
- 检查包含路径设置
- 确保BSP文件夹中的头文件存在

**问题2：重定义错误**
- 检查是否有重复的函数定义
- 确保没有包含FreeRTOS相关文件

**问题3：链接错误**
- 检查启动文件是否正确
- 确保所有.c文件都添加到工程中

### 5.3 功能测试
1. 下载程序到开发板
2. 测试基本功能：
   - LED闪烁
   - 按键响应
   - 串口通信
   - 舵机控制

## 第六步：OpenMV代码

### 6.1 复制OpenMV代码
将 `red/openMV/red.py` 复制到 `TEST_AI/openMV/red.py`

### 6.2 OpenMV代码无需修改
OpenMV代码保持完全不变，因为：
- 通信协议不变
- 数据格式不变
- 串口配置不变

## 第七步：系统集成测试

### 7.1 硬件连接
1. 连接OpenMV与STM32的串口3
2. 连接舵机到TIM3的PWM输出
3. 连接激光器到PE2
4. 连接按键到PE3-PE6

### 7.2 功能验证
1. 上电后检查初始化
2. 测试OpenMV与STM32通信
3. 测试坐标数据接收和处理
4. 测试PID控制和舵机响应
5. 测试按键功能

### 7.3 性能对比
与原FreeRTOS版本对比：
- 响应时间
- 控制精度
- 系统稳定性

## 注意事项

1. **保持原有逻辑不变**：移植过程中不改变控制算法和通信协议
2. **仔细处理中断**：确保UART中断和定时器中断正确配置
3. **内存管理**：裸机系统需要注意栈大小和变量作用域
4. **实时性考虑**：虽然去除了RTOS，但要保证控制的实时性
5. **调试方法**：使用串口输出和LED指示进行调试

## 预期结果

移植完成后应该实现：
- 与原系统完全相同的功能
- 更简洁的代码结构  
- 更容易理解和维护
- 更小的内存占用
- 相同或更好的实时性能
