#ifndef __COMMON_H
#define __COMMON_H	


typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;


typedef uint32_t  u32;
typedef uint16_t u16;
typedef uint8_t  u8;


#endif
