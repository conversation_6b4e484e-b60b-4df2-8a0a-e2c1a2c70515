#MicroXplorer Configuration settings - do not modify
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=TIM6
Mcu.IP5=USART3
Mcu.IPNb=6
Dma.Request0=USART3_RX
Dma.RequestsNb=1
Dma.USART3_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.0.Instance=DMA1_Stream1
Dma.USART3_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.0.Mode=DMA_CIRCULAR
Dma.USART3_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART3_RX
Dma.RequestsNb=1
Dma.USART3_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.0.Instance=DMA1_Stream1
Dma.USART3_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.0.Mode=DMA_CIRCULAR
Dma.USART3_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Mcu.Name=STM32F407VETx
Mcu.Package=LQFP100
Mcu.Pin0=PC13-ANTI_TAMP
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin3=PH0-OSC_IN
Mcu.Pin4=PH1-OSC_OUT
Mcu.Pin5=PE3
Mcu.Pin6=PE4
Mcu.Pin7=PE5
Mcu.Pin8=PE6
Mcu.Pin9=PB10
Mcu.Pin10=PB11
Mcu.Pin11=VP_SYS_VS_Systick
Mcu.Pin12=VP_TIM6_VS_ClockSourceINT
Mcu.PinsNb=13
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.8.1
MxDb.Version=DB.6.0.81
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM6_DAC_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Locked=true
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PC13-ANTI_TAMP.GPIOParameters=GPIO_Label
PC13-ANTI_TAMP.GPIO_Label=LED1
PC13-ANTI_TAMP.Locked=true
PC13-ANTI_TAMP.Signal=GPIO_Output
PC14-OSC32_IN.GPIOParameters=GPIO_Label
PC14-OSC32_IN.GPIO_Label=LED2
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Signal=GPIO_Output
PC15-OSC32_OUT.GPIOParameters=GPIO_Label
PC15-OSC32_OUT.GPIO_Label=LED3
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Signal=GPIO_Output
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=KEY1
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_Label
PE4.GPIO_Label=KEY2
PE4.Locked=true
PE4.Signal=GPIO_Input
PE5.GPIOParameters=GPIO_Label
PE5.GPIO_Label=KEY3
PE5.Locked=true
PE5.Signal=GPIO_Input
PE6.GPIOParameters=GPIO_Label
PE6.GPIO_Label=KEY4
PE6.Locked=true
PE6.Signal=GPIO_Input
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.27.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=UART_AI.ioc
ProjectManager.ProjectName=UART_AI
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_TIM6_Init-TIM6-false-HAL-true,5-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
TIM6.IPParameters=Prescaler,Period
TIM6.Period=999
TIM6.Prescaler=839
USART3.BaudRate=115200
USART3.DMADisableonRxError=UART_ADVFEATURE_DMA_DISABLEONRXERROR
USART3.IPParameters=VirtualMode,BaudRate,DMADisableonRxError
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
isbadioc=false
