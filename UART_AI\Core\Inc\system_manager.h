/**
  ******************************************************************************
  * @file    system_manager.h
  * @brief   系统管理模块头文件
  ******************************************************************************
  */

#ifndef __SYSTEM_MANAGER_H
#define __SYSTEM_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"

/* 系统配置参数 */
#define SYSTEM_TICK_FREQ            1000    // 系统节拍频率 (Hz)
#define WATCHDOG_TIMEOUT            5000    // 看门狗超时时间 (ms)
#define HEARTBEAT_INTERVAL          1000    // 心跳间隔 (ms)
#define STATUS_UPDATE_INTERVAL      100     // 状态更新间隔 (ms)

/* 系统事件定义 */
#define EVENT_UART_DATA_RECEIVED    0x01    // UART数据接收事件
#define EVENT_TIMER_EXPIRED         0x02    // 定时器超时事件
#define EVENT_TASK_COMPLETE         0x04    // 任务完成事件
#define EVENT_ERROR_OCCURRED        0x08    // 错误发生事件
#define EVENT_SYSTEM_RESET          0x10    // 系统复位事件

/* 系统优先级定义 */
#define PRIORITY_CRITICAL           0       // 关键优先级
#define PRIORITY_HIGH               1       // 高优先级
#define PRIORITY_NORMAL             2       // 普通优先级
#define PRIORITY_LOW                3       // 低优先级

/* 错误代码定义 */
#define ERROR_NONE                  0x00    // 无错误
#define ERROR_UART_TIMEOUT          0x01    // UART超时错误
#define ERROR_DATA_INVALID          0x02    // 数据无效错误
#define ERROR_BUFFER_OVERFLOW       0x03    // 缓冲区溢出错误
#define ERROR_CHECKSUM_FAILED       0x04    // 校验失败错误
#define ERROR_SYSTEM_FAULT          0x05    // 系统故障错误

/* 系统状态结构 */
typedef struct {
    system_state_t state;           // 系统状态
    uint32_t uptime;               // 运行时间 (ms)
    uint32_t last_heartbeat;       // 最后心跳时间
    uint8_t error_code;            // 错误代码
    uint8_t reset_count;           // 复位次数
    uint8_t cpu_usage;             // CPU使用率 (%)
    uint16_t free_memory;          // 剩余内存 (bytes)
} system_status_t;

/* 事件结构 */
typedef struct {
    uint8_t event_id;              // 事件ID
    uint8_t priority;              // 优先级
    uint32_t timestamp;            // 时间戳
    void *data;                    // 事件数据
} system_event_t;

/* 任务结构 */
typedef struct {
    void (*task_func)(void);       // 任务函数指针
    uint32_t period;               // 执行周期 (ms)
    uint32_t last_run;             // 上次运行时间
    uint8_t priority;              // 优先级
    uint8_t enabled;               // 使能标志
} system_task_t;

/* 性能监控结构 */
typedef struct {
    uint32_t task_run_count;       // 任务运行次数
    uint32_t max_execution_time;   // 最大执行时间 (us)
    uint32_t avg_execution_time;   // 平均执行时间 (us)
    uint32_t total_execution_time; // 总执行时间 (us)
} performance_monitor_t;

/* 全局变量声明 */
extern volatile system_status_t g_system_status;
extern volatile uint32_t g_system_events;
extern performance_monitor_t g_performance_monitor;

/* 函数声明 */

/**
 * @brief  系统管理模块初始化
 * @param  None
 * @retval None
 */
void System_Manager_Init(void);

/**
 * @brief  系统管理主处理函数
 * @param  None
 * @retval None
 */
void System_Manager_Process(void);

/**
 * @brief  系统状态更新
 * @param  None
 * @retval None
 */
void System_Manager_Update_Status(void);

/**
 * @brief  发送系统事件
 * @param  event_id: 事件ID
 * @param  priority: 优先级
 * @param  data: 事件数据指针
 * @retval 发送结果 (0=成功, 其他=错误码)
 */
uint8_t System_Manager_Send_Event(uint8_t event_id, uint8_t priority, void *data);

/**
 * @brief  处理系统事件
 * @param  None
 * @retval None
 */
void System_Manager_Handle_Events(void);

/**
 * @brief  注册系统任务
 * @param  task_func: 任务函数指针
 * @param  period: 执行周期 (ms)
 * @param  priority: 优先级
 * @retval 注册结果 (0=成功, 其他=错误码)
 */
uint8_t System_Manager_Register_Task(void (*task_func)(void), uint32_t period, uint8_t priority);

/**
 * @brief  任务调度器
 * @param  None
 * @retval None
 */
void System_Manager_Task_Scheduler(void);

/**
 * @brief  系统心跳处理
 * @param  None
 * @retval None
 */
void System_Manager_Heartbeat(void);

/**
 * @brief  错误处理
 * @param  error_code: 错误代码
 * @retval None
 */
void System_Manager_Handle_Error(uint8_t error_code);

/**
 * @brief  系统复位
 * @param  reset_type: 复位类型 (0=软复位, 1=硬复位)
 * @retval None
 */
void System_Manager_Reset(uint8_t reset_type);

/**
 * @brief  获取系统状态
 * @param  None
 * @retval 系统状态指针
 */
system_status_t* System_Manager_Get_Status(void);

/**
 * @brief  设置系统状态
 * @param  state: 系统状态
 * @retval None
 */
void System_Manager_Set_State(system_state_t state);

/**
 * @brief  系统自检
 * @param  None
 * @retval 自检结果 (0=通过, 其他=错误码)
 */
uint8_t System_Manager_Self_Test(void);

/**
 * @brief  性能监控
 * @param  None
 * @retval None
 */
void System_Manager_Performance_Monitor(void);

/**
 * @brief  内存使用监控
 * @param  None
 * @retval 剩余内存大小 (bytes)
 */
uint16_t System_Manager_Get_Free_Memory(void);

/**
 * @brief  CPU使用率计算
 * @param  None
 * @retval CPU使用率 (%)
 */
uint8_t System_Manager_Get_CPU_Usage(void);

/**
 * @brief  看门狗喂狗
 * @param  None
 * @retval None
 */
void System_Manager_Feed_Watchdog(void);

/**
 * @brief  系统时间获取
 * @param  None
 * @retval 系统时间 (ms)
 */
uint32_t System_Manager_Get_Tick(void);

/**
 * @brief  延时函数
 * @param  delay_ms: 延时时间 (ms)
 * @retval None
 */
void System_Manager_Delay(uint32_t delay_ms);

/**
 * @brief  进入低功耗模式
 * @param  mode: 低功耗模式类型
 * @retval None
 */
void System_Manager_Enter_Low_Power(uint8_t mode);

/**
 * @brief  退出低功耗模式
 * @param  None
 * @retval None
 */
void System_Manager_Exit_Low_Power(void);

#ifdef __cplusplus
}
#endif

#endif /* __SYSTEM_MANAGER_H */
