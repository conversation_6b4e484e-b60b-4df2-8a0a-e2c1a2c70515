/**
  ******************************************************************************
  * @file    uart_comm.h
  * @brief   UART通信和数据处理模块头文件
  ******************************************************************************
  */

#ifndef __UART_COMM_H
#define __UART_COMM_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "usart.h"

/* 通信协议定义 */
#define FRAME_HEADER_A1         0xA1    // 帧头标识
#define FRAME_TAIL_1A           0x1A    // 帧尾标识
#define CMD_SERVO_Y             0x12    // Y轴舵机位置命令
#define CMD_SERVO_X             0x13    // X轴舵机位置命令
#define CMD_COORD_ERROR         0x02    // 坐标误差数据
#define CMD_TASK_COMPLETE       0x11    // 任务完成命令
#define CMD_ACK                 0x33    // 应答命令
#define CMD_POINT_UPDATE        0x44    // 点位更新命令

/* 数据缓冲区大小 */
#define UART_RX_BUFFER_SIZE     255
#define UART_TX_BUFFER_SIZE     64

/* 通信状态定义 */
typedef enum {
    UART_STATE_IDLE = 0,
    UART_STATE_RECEIVING,
    UART_STATE_PROCESSING,
    UART_STATE_ERROR
} uart_state_t;

/* 数据包结构定义 */
typedef struct {
    uint8_t header;         // 帧头
    uint8_t cmd;           // 命令字
    uint8_t data[16];      // 数据域
    uint8_t length;        // 数据长度
    uint8_t tail;          // 帧尾
} uart_packet_t;

/* 坐标误差数据结构 */
typedef struct {
    uint8_t hori_dir;      // 水平方向标志 (0=正, 1=负)
    uint8_t hori_value;    // 水平误差值
    uint8_t vert_dir;      // 垂直方向标志 (0=正, 1=负)
    uint8_t vert_value;    // 垂直误差值
} coord_error_t;

/* 全局变量声明 */
extern volatile uint8_t uart_rx_flag;          // UART接收完成标志
extern volatile uint8_t uart_data_ready;       // 数据准备就绪标志
extern volatile uint16_t uart_rx_length;       // 接收数据长度
extern uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];  // 接收缓冲区
extern uint8_t uart_tx_buffer[UART_TX_BUFFER_SIZE];  // 发送缓冲区

/* 通信相关标志位 */
extern volatile uint8_t rec_flag;              // 接收标志
extern volatile uint8_t rec_ok_flag;           // 接收完成标志
extern volatile uint8_t rx_ack;                // 接收应答标志
extern volatile uint8_t task;                  // 当前任务号
extern volatile uint8_t point_state;           // 点状态

/* 坐标误差变量 */
extern volatile float servo_hori_error;        // 水平舵机误差
extern volatile float servo_vert_error;        // 垂直舵机误差

/* 函数声明 */

/**
 * @brief  UART通信模块初始化
 * @param  None
 * @retval None
 */
void UART_Comm_Init(void);

/**
 * @brief  UART数据接收处理
 * @param  None
 * @retval None
 */
void UART_Data_Process(void);

/**
 * @brief  解析接收到的数据包
 * @param  buffer: 数据缓冲区
 * @param  length: 数据长度
 * @retval 解析结果 (0=成功, 1=失败)
 */
uint8_t UART_Parse_Packet(uint8_t *buffer, uint16_t length);

/**
 * @brief  处理舵机位置命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @retval None
 */
void UART_Handle_Servo_Cmd(uint8_t cmd, uint8_t *data);

/**
 * @brief  处理控制命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval None
 */
void UART_Handle_Control_Cmd(uint8_t cmd, uint8_t *data, uint8_t length);

/**
 * @brief  处理坐标误差数据
 * @param  data: 数据指针
 * @retval None
 */
void UART_Handle_Coord_Error(uint8_t *data);

/**
 * @brief  发送任务号
 * @param  task_num: 任务号
 * @retval None
 */
void UART_Send_Task_Number(uint8_t task_num);

/**
 * @brief  发送应答信号
 * @param  None
 * @retval None
 */
void UART_Send_ACK(void);

/**
 * @brief  发送任务完成信号
 * @param  None
 * @retval None
 */
void UART_Send_Task_Complete(void);

/**
 * @brief  UART空闲中断回调函数
 * @param  huart: UART句柄
 * @retval None
 */
void UART_IDLE_Callback(UART_HandleTypeDef *huart);

/**
 * @brief  获取通信状态
 * @param  None
 * @retval 通信状态
 */
uart_state_t UART_Get_State(void);

/**
 * @brief  重置通信状态
 * @param  None
 * @retval None
 */
void UART_Reset_State(void);

#ifdef __cplusplus
}
#endif

#endif /* __UART_COMM_H */
