/**
  ******************************************************************************
  * @file    data_process.h
  * @brief   数据处理和控制逻辑模块头文件
  ******************************************************************************
  */

#ifndef __DATA_PROCESS_H
#define __DATA_PROCESS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "uart_comm.h"
#include "pid.h"

/* 控制模式定义 */
typedef enum {
    CONTROL_MODE_IDLE = 0,      // 空闲模式
    CONTROL_MODE_POINT,         // 点位控制模式
    CONTROL_MODE_TRACK,         // 轨迹跟踪模式
    CONTROL_MODE_RESET          // 复位模式
} control_mode_t;

/* 任务状态定义 */
typedef enum {
    TASK_STATE_INIT = 0,        // 初始化状态
    TASK_STATE_RUNNING,         // 运行状态
    TASK_STATE_COMPLETE,        // 完成状态
    TASK_STATE_ERROR            // 错误状态
} task_state_t;

/* 舵机控制参数结构 */
typedef struct {
    float hori_all;             // 水平舵机累计值
    float vert_all;             // 垂直舵机累计值
    uint16_t midx;              // 中位X值
    uint16_t midy;              // 中位Y值
    uint16_t savex;             // 保存X值
    uint16_t savey;             // 保存Y值
} servo_control_t;

/* 系统状态结构 */
typedef struct {
    control_mode_t mode;        // 控制模式
    task_state_t state;         // 任务状态
    uint8_t current_task;       // 当前任务号
    uint8_t pause_flag;         // 暂停标志
    uint8_t reset_flag;         // 复位标志
} system_status_t;

/* 全局变量声明 */
extern servo_control_t servo_ctrl;
extern system_status_t sys_status;
extern pid_t pid_SERVOX;
extern pid_t pid_SERVOY;
extern pid_t pid_POINTX;
extern pid_t pid_POINTY;

/* 函数声明 */

/**
 * @brief  数据处理模块初始化
 * @param  None
 * @retval None
 */
void DataProcess_Init(void);

/**
 * @brief  主数据处理任务
 * @param  None
 * @retval None
 */
void DataProcess_Task(void);

/**
 * @brief  舵机控制处理
 * @param  None
 * @retval None
 */
void ServoControl_Process(void);

/**
 * @brief  任务1控制处理（点位控制）
 * @param  None
 * @retval None
 */
void Task1_Control_Process(void);

/**
 * @brief  任务2-10控制处理（轨迹跟踪）
 * @param  task_num: 任务号
 * @retval None
 */
void Task_Track_Control_Process(uint8_t task_num);

/**
 * @brief  复位模式处理
 * @param  None
 * @retval None
 */
void Reset_Mode_Process(void);

/**
 * @brief  设置舵机中位
 * @param  None
 * @retval None
 */
void Servo_Set_Middle_Position(void);

/**
 * @brief  舵机回到中位
 * @param  None
 * @retval None
 */
void Servo_Return_Middle(void);

/**
 * @brief  更新舵机位置
 * @param  hori_delta: 水平增量
 * @param  vert_delta: 垂直增量
 * @retval None
 */
void Servo_Update_Position(float hori_delta, float vert_delta);

/**
 * @brief  获取当前控制模式
 * @param  None
 * @retval 控制模式
 */
control_mode_t DataProcess_Get_Mode(void);

/**
 * @brief  设置控制模式
 * @param  mode: 控制模式
 * @retval None
 */
void DataProcess_Set_Mode(control_mode_t mode);

/**
 * @brief  获取任务状态
 * @param  None
 * @retval 任务状态
 */
task_state_t DataProcess_Get_Task_State(void);

/**
 * @brief  设置任务状态
 * @param  state: 任务状态
 * @retval None
 */
void DataProcess_Set_Task_State(task_state_t state);

/**
 * @brief  切换到下一个任务
 * @param  None
 * @retval None
 */
void DataProcess_Next_Task(void);

/**
 * @brief  暂停/继续任务
 * @param  None
 * @retval None
 */
void DataProcess_Toggle_Pause(void);

/**
 * @brief  复位系统
 * @param  None
 * @retval None
 */
void DataProcess_Reset_System(void);

/**
 * @brief  处理特殊命令
 * @param  None
 * @retval None
 */
void DataProcess_Handle_Special_Command(void);

/**
 * @brief  系统状态监控
 * @param  None
 * @retval None
 */
void DataProcess_Status_Monitor(void);

#ifdef __cplusplus
}
#endif

#endif /* __DATA_PROCESS_H */
