# 运动控制系统移植指南（去除FreeRTOS版本）

## 1. 工程概述

### 原工程架构
- **MCU**: STM32F407VET6
- **操作系统**: FreeRTOS
- **主要功能**: 激光指向控制、视觉伺服、PID控制
- **通信**: UART3与OpenMV通信

### 移植目标
- 移除FreeRTOS依赖
- 改为裸机轮询+中断方式
- 保持原有功能不变
- 代码结构清晰易维护

## 2. 文件结构规划

```
TEST_AI/
├── Core/
│   ├── Inc/
│   │   ├── main.h
│   │   ├── stm32f4xx_hal_conf.h
│   │   ├── stm32f4xx_it.h
│   │   ├── gpio.h
│   │   ├── dma.h
│   │   ├── i2c.h
│   │   ├── spi.h
│   │   ├── tim.h
│   │   └── usart.h
│   └── Src/
│       ├── main.c
│       ├── stm32f4xx_it.c
│       ├── stm32f4xx_hal_msp.c
│       ├── system_stm32f4xx.c
│       ├── gpio.c
│       ├── dma.c
│       ├── i2c.c
│       ├── spi.c
│       ├── tim.c
│       └── usart.c
├── Drivers/
│   ├── STM32F4xx_HAL_Driver/ (HAL库文件)
│   ├── CMSIS/ (CMSIS文件)
│   └── BSP/
│       ├── control.c/h
│       ├── pid.c/h
│       ├── motor.c/h
│       ├── encoder.c/h
│       ├── mpu6050.c/h
│       ├── oled.c/h
│       ├── key.c/h
│       ├── bsp_uart.c/h
│       └── 24l01.c/h
├── MDK-ARM/
│   ├── TEST_AI.uvprojx
│   ├── TEST_AI.uvoptx
│   └── startup_stm32f407xx.s
└── openMV/
    └── red.py (OpenMV代码，保持不变)
```

## 3. 硬件配置保持不变

### 时钟配置
- 外部晶振: 8MHz HSE
- 系统时钟: 168MHz
- APB1: 42MHz
- APB2: 84MHz

### 外设配置
- **TIM3**: PWM输出 (舵机控制)
  - CH3: SERVOX (激光器X轴)
  - CH4: SERVOY (激光器Y轴)
- **TIM4**: 编码器输入 (右轮)
- **TIM5**: 编码器输入 (左轮)
- **TIM6**: 定时器中断 (系统节拍)
- **TIM8**: PWM输出 (电机控制)
- **USART1**: 调试串口
- **USART3**: 与OpenMV通信 (DMA接收)
- **I2C1**: MPU6050通信
- **SPI1**: OLED显示

### GPIO配置
- **激光器控制**: 保持原配置
- **按键输入**: 保持原配置
- **编码器输入**: 保持原配置

## 4. 核心修改策略

### 4.1 任务调度改为轮询
原FreeRTOS任务 → 主循环中的状态机

### 4.2 信号量改为标志位
```c
// 原: osSemaphoreWait(UartRecSemHandle, portMAX_DELAY);
// 改为: while(!uart_data_ready_flag);
```

### 4.3 延时函数
```c
// 原: osDelay(10);
// 改为: HAL_Delay(10);
```

### 4.4 中断处理保持不变
- UART空闲中断
- 定时器中断
- 编码器中断

## 5. 主要修改文件

### 5.1 main.c 主要修改
- 移除FreeRTOS相关头文件
- 移除MX_FREERTOS_Init()调用
- 移除osKernelStart()
- 在while(1)中实现原任务逻辑

### 5.2 新增系统管理文件
- system_task.c/h: 实现原FreeRTOS任务逻辑
- system_flags.h: 定义全局标志位

### 5.3 中断文件修改
- 移除FreeRTOS信号量
- 改为设置标志位

## 6. 移植步骤

### 步骤1: 创建基础工程结构
### 步骤2: 复制并修改CubeMX配置
### 步骤3: 复制BSP驱动文件
### 步骤4: 修改主函数和中断处理
### 步骤5: 实现任务调度逻辑
### 步骤6: 配置Keil工程
### 步骤7: 编译调试

## 7. 注意事项

1. **保持原有变量定义不变**
2. **保持PID控制逻辑不变**
3. **保持通信协议不变**
4. **保持硬件配置不变**
5. **OpenMV代码完全不变**

## 8. 预期效果

移植完成后应实现：
- 与原工程完全相同的功能
- 更简单的代码结构
- 更容易调试和维护
- 更小的内存占用
