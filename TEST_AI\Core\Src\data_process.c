/**
  ******************************************************************************
  * @file    data_process.c
  * @brief   数据处理和控制逻辑模块实现
  ******************************************************************************
  */

#include "data_process.h"
#include "uart_comm.h"
#include "control.h"
#include "key.h"
#include "oled.h"
#include <stdio.h>

/* 全局变量定义 */
servo_control_t servo_ctrl = {
    .hori_all = 1400,
    .vert_all = 2500,
    .midx = 0,
    .midy = 0,
    .savex = 0,
    .savey = 0
};

system_status_t sys_status = {
    .mode = CONTROL_MODE_IDLE,
    .state = TASK_STATE_INIT,
    .current_task = 0,
    .pause_flag = 0,
    .reset_flag = 0
};

/* PID控制器实例 */
pid_t pid_SERVOX;
pid_t pid_SERVOY;
pid_t pid_POINTX;
pid_t pid_POINTY;

/**
 * @brief  数据处理模块初始化
 * @param  None
 * @retval None
 */
void DataProcess_Init(void)
{
    /* 初始化PID控制器 */
    PID_SERVOX_Init(&pid_SERVOX);
    PID_SERVOX_Init(&pid_SERVOY);
    PID_SERVO_POINTX_Init(&pid_POINTX);
    PID_SERVO_POINTY_Init(&pid_POINTY);
    
    /* 初始化舵机控制参数 */
    servo_ctrl.hori_all = 1400;
    servo_ctrl.vert_all = 2500;
    servo_ctrl.midx = 0;
    servo_ctrl.midy = 0;
    servo_ctrl.savex = 0;
    servo_ctrl.savey = 0;
    
    /* 初始化系统状态 */
    sys_status.mode = CONTROL_MODE_IDLE;
    sys_status.state = TASK_STATE_INIT;
    sys_status.current_task = 0;
    sys_status.pause_flag = 0;
    sys_status.reset_flag = 0;
    
    /* 设置舵机初始位置 */
    SERVOX = (uint16_t)servo_ctrl.hori_all;
    SERVOY = (uint16_t)servo_ctrl.vert_all;
}

/**
 * @brief  主数据处理任务
 * @param  None
 * @retval None
 */
void DataProcess_Task(void)
{
    /* 处理UART数据 */
    UART_Data_Process();
    
    /* 舵机控制处理 */
    ServoControl_Process();
    
    /* 系统状态监控 */
    DataProcess_Status_Monitor();
}

/**
 * @brief  舵机控制处理
 * @param  None
 * @retval None
 */
void ServoControl_Process(void)
{
    if (sys_status.pause_flag) {
        return;  // 暂停状态，不执行控制
    }
    
    switch (sys_status.mode) {
        case CONTROL_MODE_RESET:
            Reset_Mode_Process();
            break;
            
        case CONTROL_MODE_POINT:
            Task1_Control_Process();
            break;
            
        case CONTROL_MODE_TRACK:
            Task_Track_Control_Process(sys_status.current_task);
            break;
            
        default:
            break;
    }
}

/**
 * @brief  任务1控制处理（点位控制）
 * @param  None
 * @retval None
 */
void Task1_Control_Process(void)
{
    if (sys_status.current_task == 1 && rec_ok_flag == 0) {
        /* 发送任务号 */
        if (rx_ack == 0) {
            UART_Send_Task_Number(1);
        }
        
        /* PID控制 */
        if (rec_flag == 1) {
            rec_flag = 0;
            
            float hori_delta = PID_SERVO(&pid_POINTX, -servo_hori_error);
            float vert_delta = PID_SERVO(&pid_POINTY, -servo_vert_error);
            
            Servo_Update_Position(hori_delta, vert_delta);
        }
    }
}

/**
 * @brief  任务2-10控制处理（轨迹跟踪）
 * @param  task_num: 任务号
 * @retval None
 */
void Task_Track_Control_Process(uint8_t task_num)
{
    if (task_num >= 2 && task_num <= 10 && rec_ok_flag == 0) {
        /* 发送任务号 */
        if (rx_ack == 0) {
            UART_Send_Task_Number(task_num);
        }
        
        /* PID控制 */
        if (rec_flag == 1) {
            rec_flag = 0;
            
            float hori_delta = PID_SERVO(&pid_SERVOX, -servo_hori_error);
            float vert_delta = PID_SERVO(&pid_SERVOY, -servo_vert_error);
            
            Servo_Update_Position(hori_delta, vert_delta);
        }
    }
}

/**
 * @brief  复位模式处理
 * @param  None
 * @retval None
 */
void Reset_Mode_Process(void)
{
    /* 复位模式的具体处理逻辑 */
    if (sys_status.reset_flag == 1) {
        /* 执行复位操作 */
        Servo_Return_Middle();
        sys_status.reset_flag = 0;
        sys_status.mode = CONTROL_MODE_IDLE;
    }
}

/**
 * @brief  设置舵机中位
 * @param  None
 * @retval None
 */
void Servo_Set_Middle_Position(void)
{
    if (sys_status.current_task == 1) {
        servo_ctrl.midx = SERVOX;
        servo_ctrl.midy = SERVOY;
    }
}

/**
 * @brief  舵机回到中位
 * @param  None
 * @retval None
 */
void Servo_Return_Middle(void)
{
    servo_ctrl.hori_all = servo_ctrl.midx;
    servo_ctrl.vert_all = servo_ctrl.midy;
    SERVOX = (uint16_t)servo_ctrl.hori_all;
    SERVOY = (uint16_t)servo_ctrl.vert_all;
}

/**
 * @brief  更新舵机位置
 * @param  hori_delta: 水平增量
 * @param  vert_delta: 垂直增量
 * @retval None
 */
void Servo_Update_Position(float hori_delta, float vert_delta)
{
    servo_ctrl.hori_all += hori_delta;
    servo_ctrl.vert_all += vert_delta;
    
    /* 限制舵机范围 */
    if (servo_ctrl.hori_all < 500) servo_ctrl.hori_all = 500;
    if (servo_ctrl.hori_all > 2500) servo_ctrl.hori_all = 2500;
    if (servo_ctrl.vert_all < 500) servo_ctrl.vert_all = 500;
    if (servo_ctrl.vert_all > 2500) servo_ctrl.vert_all = 2500;
    
    SERVOX = (uint16_t)servo_ctrl.hori_all;
    SERVOY = (uint16_t)servo_ctrl.vert_all;
}

/**
 * @brief  获取当前控制模式
 * @param  None
 * @retval 控制模式
 */
control_mode_t DataProcess_Get_Mode(void)
{
    return sys_status.mode;
}

/**
 * @brief  设置控制模式
 * @param  mode: 控制模式
 * @retval None
 */
void DataProcess_Set_Mode(control_mode_t mode)
{
    sys_status.mode = mode;
}

/**
 * @brief  获取任务状态
 * @param  None
 * @retval 任务状态
 */
task_state_t DataProcess_Get_Task_State(void)
{
    return sys_status.state;
}

/**
 * @brief  设置任务状态
 * @param  state: 任务状态
 * @retval None
 */
void DataProcess_Set_Task_State(task_state_t state)
{
    sys_status.state = state;
}

/**
 * @brief  切换到下一个任务
 * @param  None
 * @retval None
 */
void DataProcess_Next_Task(void)
{
    sys_status.current_task++;
    rec_ok_flag = 0;
    rx_ack = 0;
    
    /* 根据任务号设置控制模式 */
    if (sys_status.current_task == 1) {
        sys_status.mode = CONTROL_MODE_POINT;
    } else if (sys_status.current_task >= 2 && sys_status.current_task <= 10) {
        sys_status.mode = CONTROL_MODE_TRACK;
    } else {
        sys_status.mode = CONTROL_MODE_IDLE;
    }
}

/**
 * @brief  暂停/继续任务
 * @param  None
 * @retval None
 */
void DataProcess_Toggle_Pause(void)
{
    sys_status.pause_flag ^= 1;
}

/**
 * @brief  复位系统
 * @param  None
 * @retval None
 */
void DataProcess_Reset_System(void)
{
    sys_status.reset_flag = 1;
    sys_status.mode = CONTROL_MODE_RESET;
}

/**
 * @brief  处理特殊命令
 * @param  None
 * @retval None
 */
void DataProcess_Handle_Special_Command(void)
{
    if (point_state < 5) {
        HAL_UART_Transmit(&huart3, "9", 1, 100);
    } else {
        rec_ok_flag = 0;
        rx_ack = 0;
    }
}

/**
 * @brief  系统状态监控
 * @param  None
 * @retval None
 */
void DataProcess_Status_Monitor(void)
{
    static uint32_t last_monitor_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    /* 每100ms更新一次状态显示 */
    if (current_time - last_monitor_time >= 100) {
        last_monitor_time = current_time;
        
        char status_str[32];
        sprintf(status_str, "Task:%d Mode:%d", sys_status.current_task, sys_status.mode);
        OLED_ShowString(0, 2, status_str);
        
        /* 检查任务完成条件 */
        if (rec_ok_flag == 1) {
            Servo_Set_Middle_Position();
            sys_status.state = TASK_STATE_COMPLETE;
        }
    }
}
