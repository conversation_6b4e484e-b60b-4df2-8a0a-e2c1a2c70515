/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LED1_Pin GPIO_PIN_13
#define LED1_GPIO_Port GPIOC
#define LED2_Pin GPIO_PIN_14
#define LED2_GPIO_Port GPIOC
#define LED3_Pin GPIO_PIN_15
#define LED3_GPIO_Port GPIOC
#define KEY1_Pin GPIO_PIN_3
#define KEY1_GPIO_Port GPIOE
#define KEY2_Pin GPIO_PIN_4
#define KEY2_GPIO_Port GPIOE
#define KEY3_Pin GPIO_PIN_5
#define KEY3_GPIO_Port GPIOE
#define KEY4_Pin GPIO_PIN_6
#define KEY4_GPIO_Port GPIOE

/* USER CODE BEGIN Private defines */

/* 系统配置参数 */
#define SYSTEM_CLOCK_FREQ       168000000   // 系统时钟频率
#define UART_BAUDRATE          115200       // UART波特率
#define UART_BUFFER_SIZE       255          // UART缓冲区大小

/* 外设句柄声明 */
extern UART_HandleTypeDef huart3;
extern DMA_HandleTypeDef hdma_usart3_rx;
extern TIM_HandleTypeDef htim6;

/* 系统状态定义 */
typedef enum {
    SYS_STATE_INIT = 0,         // 初始化状态
    SYS_STATE_READY,            // 就绪状态
    SYS_STATE_RUNNING,          // 运行状态
    SYS_STATE_ERROR             // 错误状态
} system_state_t;

/* 全局变量声明 */
extern volatile system_state_t g_system_state;
extern volatile uint32_t g_system_tick;

/* 系统函数声明 */
void System_Init(void);
void System_Process(void);
void System_Error_Handler(void);

/* LED控制函数 */
void LED_On(uint8_t led_num);
void LED_Off(uint8_t led_num);
void LED_Toggle(uint8_t led_num);

/* 延时函数 */
void Delay_ms(uint32_t ms);
void Delay_us(uint32_t us);

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
