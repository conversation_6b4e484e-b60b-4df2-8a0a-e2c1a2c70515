/**
 ******************************************************************************
 * @file    system_flags.c
 * @brief   系统标志位实现文件
 ******************************************************************************
 */

#include "system_flags.h"
#include "pid.h"
#include "motor.h"
#include "encoder.h"

/* 系统标志位定义 */
volatile uint8_t uart_data_ready_flag = 0; // UART数据接收完成标志
volatile uint8_t system_10ms_flag = 0;     // 10ms系统节拍标志
volatile uint8_t key_scan_flag = 0;        // 按键扫描标志
volatile uint8_t servo_update_flag = 0;    // 舵机更新标志

/* 通信相关标志 */
volatile uint8_t rec_flag = 0;    // 接收标志
volatile uint8_t rec_ok_flag = 0; // 接收完成标志
volatile uint8_t rx_ack = 0;      // 接收应答标志
volatile uint8_t delay_flag = 0;  // 延时标志
volatile uint8_t pause_flag = 0;  // 暂停标志
volatile uint8_t reset_flag = 0;  // 复位标志
volatile uint8_t point_state = 0; // 点状态

/* 任务相关变量 */
volatile uint8_t task = 0; // 当前任务号

/* UART接收相关 */
uint8_t rx_len = 0;    // 接收数据长度
uint8_t RxBuffer[255]; // 接收缓冲区

/* 控制相关变量 */
int Encoder_All_L = 0;      // 左编码器累计值
int Encoder_All_R = 0;      // 右编码器累计值
int dir_error = 0;          // 方向误差
float angle = 0;            // 角度
float servo_hori_error = 0; // 水平舵机误差
float servo_vert_error = 0; // 垂直舵机误差

float servo_hori_all = 1400; // 水平舵机累计值
float servo_vert_all = 2500; // 垂直舵机累计值
uint16_t servo_midx = 0;     // 舵机中位X
uint16_t servo_midy = 0;     // 舵机中位Y
uint16_t servo_savex = 0;    // 舵机保存X
uint16_t servo_savey = 0;    // 舵机保存Y

/* 外部变量声明 */
extern Motor_t motor;
extern Encoder_t encoder;
extern MPU6050_t MPU6050;
extern pid_t pid_SERVOX;
extern pid_t pid_SERVOY;
extern pid_t pid_POINT;
extern pid_t pid_POINTX;
extern pid_t pid_POINTY;
extern Servo_t servo;
extern Point_t pos, LU, LD, RU, RD;

/**
 * @brief  系统标志位初始化
 * @param  None
 * @retval None
 */
void SystemFlags_Init(void)
{
  /* 清除所有标志位 */
  SystemFlags_Clear();

  /* 初始化控制变量 */
  servo_hori_all = 1400;
  servo_vert_all = 2500;
  servo_midx = 0;
  servo_midy = 0;
  servo_savex = 0;
  servo_savey = 0;

  Encoder_All_L = 0;
  Encoder_All_R = 0;
  dir_error = 0;
  angle = 0;
  servo_hori_error = 0;
  servo_vert_error = 0;

  task = 0;
  point_state = 0;
}

/**
 * @brief  清除系统标志位
 * @param  None
 * @retval None
 */
void SystemFlags_Clear(void)
{
  uart_data_ready_flag = 0;
  system_10ms_flag = 0;
  key_scan_flag = 0;
  servo_update_flag = 0;

  rec_flag = 0;
  rec_ok_flag = 0;
  rx_ack = 0;
  delay_flag = 0;
  pause_flag = 0;
  reset_flag = 0;

  rx_len = 0;
}

/**
 * @brief  系统任务初始化
 * @param  None
 * @retval None
 */
void SystemTask_Init(void)
{
  /* 初始化PID控制器 */
  PID_SERVOX_Init(&pid_SERVOX);
  PID_SERVOX_Init(&pid_SERVOY);
  PID_SERVO_POINT_Init(&pid_POINT);
  PID_SERVO_POINTX_Init(&pid_POINTX);
  PID_SERVO_POINTY_Init(&pid_POINTY);

  /* 初始化电机和编码器 */
  MotorInit(&motor);
  Encoder_Init(&encoder);

  /* 初始化系统标志位 */
  SystemFlags_Init();
}

/**
 * @brief  系统任务处理主函数
 * @param  None
 * @retval None
 */
void SystemTask_Process(void)
{
  /* UART数据处理任务 */
  if (uart_data_ready_flag)
  {
    uart_data_ready_flag = 0;
    UartTask_Process();
  }

  /* 舵机控制任务 */
  if (system_10ms_flag)
  {
    system_10ms_flag = 0;
    ServoTask_Process();
  }

  /* 按键扫描任务 */
  if (key_scan_flag)
  {
    key_scan_flag = 0;
    KeyTask_Process();
  }
}

/**
 * @brief  UART数据处理任务
 * @param  None
 * @retval None
 */
void UartTask_Process(void)
{
  char txt[40];

  /* 处理舵机位置命令 */
  if (RxBuffer[0] == 0x12)
  {
    OLED_ShowString(0, 0, "ok");
    SERVOY = RxBuffer[1] << 8 | RxBuffer[2];
  }
  if (RxBuffer[0] == 0x13)
  {
    OLED_ShowString(0, 0, "ok");
    SERVOX = RxBuffer[1] << 8 | RxBuffer[2];
  }

  /* 处理控制命令 */
  if (RxBuffer[0] == 0xA1 && RxBuffer[rx_len - 1] == 0x1A)
  {
    if (RxBuffer[1] == 0x11)
    {
      if (task == 1)
      {
        servo_midx = SERVOX;
        servo_midy = SERVOY;
      }
      rec_ok_flag = 1;
    }
    if (RxBuffer[1] == 0x33)
    {
      rx_ack = 1;
    }
    if (RxBuffer[1] == 0x44)
    {
      point_state++;
      sprintf(txt, "point_num%d", point_state);
      OLED_ShowString(0, 0, txt);
    }
    else if (RxBuffer[1] == 0x02)
    {
      /* 解析坐标误差数据 */
      if (RxBuffer[2] == 1)
        servo_hori_error = -RxBuffer[3];
      else
        servo_hori_error = RxBuffer[3];
      if (RxBuffer[4] == 1)
        servo_vert_error = -RxBuffer[5];
      else
        servo_vert_error = RxBuffer[5];
      rec_flag = 1;
    }
  }
}

/**
 * @brief  舵机控制任务
 * @param  None
 * @retval None
 */
void ServoTask_Process(void)
{
  /* 根据不同任务执行不同的控制策略 */
  if (reset_flag == 1)
  {
    /* 复位模式处理 */
  }
  else if (task == 1 && rec_ok_flag == 0)
  {
    if (rx_ack == 0)
      HAL_UART_Transmit(&huart3, "1", 1, 100);
    if (rec_flag == 1)
    {
      rec_flag = 0;
      servo_hori_all += PID_SERVO(&pid_POINTX, -servo_hori_error);
      servo_vert_all += PID_SERVO(&pid_POINTY, -servo_vert_error);
      SERVOX = (uint16_t)servo_hori_all;
      SERVOY = (uint16_t)servo_vert_all;
    }
  }
  else if (task >= 2 && task <= 10 && rec_ok_flag == 0)
  {
    /* 发送任务号 */
    if (rx_ack == 0)
    {
      char task_str[2];
      sprintf(task_str, "%d", (task == 5 || task == 7 || task == 9) ? 3 : (task == 6 || task == 8 || task == 10) ? 4
                                                                                                                 : task);
      HAL_UART_Transmit(&huart3, task_str, 1, 100);
    }

    /* PID控制 */
    if (rec_flag == 1)
    {
      rec_flag = 0;
      servo_hori_all += PID_SERVO(&pid_SERVOX, -servo_hori_error);
      servo_vert_all += PID_SERVO(&pid_SERVOY, -servo_vert_error);
      SERVOX = (uint16_t)servo_hori_all;
      SERVOY = (uint16_t)servo_vert_all;
    }
  }
}

/**
 * @brief  按键扫描任务
 * @param  None
 * @retval None
 */
void KeyTask_Process(void)
{
  switch (Key_Scan())
  {
  case 1:
    /* 按键1：回到中位 */
    servo_hori_all = servo_midx;
    servo_vert_all = servo_midy;
    SERVOX = (uint16_t)servo_hori_all;
    SERVOY = (uint16_t)servo_vert_all;
    break;

  case 2:
    /* 按键2：暂停/继续 */
    pause_flag ^= 1;
    break;

  case 4:
    /* 按键4：下一个任务 */
    task++;
    rec_ok_flag = 0;
    rx_ack = 0;
    break;

  case 3:
    /* 按键3：特殊处理 */
    if (point_state < 5)
      HAL_UART_Transmit(&huart3, "9", 1, 100);
    else
    {
      rec_ok_flag = 0;
      rx_ack = 0;
    }
    break;

  default:
    break;
  }
}
