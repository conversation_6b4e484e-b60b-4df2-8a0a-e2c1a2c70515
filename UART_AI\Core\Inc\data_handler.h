/**
  ******************************************************************************
  * @file    data_handler.h
  * @brief   数据处理模块头文件
  ******************************************************************************
  */

#ifndef __DATA_HANDLER_H
#define __DATA_HANDLER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "uart_protocol.h"

/* 数据处理配置参数 */
#define MAX_COORD_ERROR             255     // 最大坐标误差值
#define MAX_SERVO_POSITION          2500    // 最大舵机位置
#define MIN_SERVO_POSITION          500     // 最小舵机位置
#define SERVO_CENTER_X              1500    // X轴舵机中位
#define SERVO_CENTER_Y              1500    // Y轴舵机中位

#define MAX_POINTS                  32      // 最大点数
#define MAX_TASKS                   10      // 最大任务数

/* 数据处理状态定义 */
typedef enum {
    DATA_STATE_IDLE = 0,            // 空闲状态
    DATA_STATE_PROCESSING,          // 处理状态
    DATA_STATE_COMPLETE,            // 完成状态
    DATA_STATE_ERROR                // 错误状态
} data_state_t;

/* 控制模式定义 */
typedef enum {
    CONTROL_MODE_MANUAL = 0,        // 手动模式
    CONTROL_MODE_AUTO,              // 自动模式
    CONTROL_MODE_TRACK,             // 轨迹模式
    CONTROL_MODE_CALIBRATE          // 校准模式
} control_mode_t;

/* 坐标点结构 */
typedef struct {
    float x;                        // X坐标
    float y;                        // Y坐标
    uint8_t valid;                  // 有效标志
} point_t;

/* 坐标误差结构 */
typedef struct {
    float hori_error;               // 水平误差
    float vert_error;               // 垂直误差
    uint32_t timestamp;             // 时间戳
    uint8_t valid;                  // 有效标志
} coord_error_t;

/* 舵机控制结构 */
typedef struct {
    uint16_t current_x;             // 当前X位置
    uint16_t current_y;             // 当前Y位置
    uint16_t target_x;              // 目标X位置
    uint16_t target_y;              // 目标Y位置
    uint16_t center_x;              // 中心X位置
    uint16_t center_y;              // 中心Y位置
    uint8_t enabled;                // 使能标志
} servo_control_t;

/* 任务控制结构 */
typedef struct {
    uint8_t current_task;           // 当前任务号
    uint8_t task_state;             // 任务状态
    uint8_t point_index;            // 当前点索引
    uint8_t total_points;           // 总点数
    point_t points[MAX_POINTS];     // 点数组
    uint32_t start_time;            // 开始时间
    uint32_t timeout;               // 超时时间
} task_control_t;

/* 数据处理统计 */
typedef struct {
    uint32_t total_processed;       // 总处理数据数
    uint32_t coord_errors_processed; // 坐标误差处理数
    uint32_t servo_commands_processed; // 舵机命令处理数
    uint32_t tasks_completed;       // 完成任务数
    uint32_t processing_errors;     // 处理错误数
} data_stats_t;

/* 全局变量声明 */
extern coord_error_t g_coord_error;        // 当前坐标误差
extern servo_control_t g_servo_control;    // 舵机控制
extern task_control_t g_task_control;      // 任务控制
extern data_stats_t g_data_stats;          // 数据统计

extern volatile uint8_t g_data_ready_flag; // 数据就绪标志
extern volatile control_mode_t g_control_mode; // 控制模式

/* 函数声明 */

/**
 * @brief  数据处理模块初始化
 * @param  None
 * @retval None
 */
void Data_Handler_Init(void);

/**
 * @brief  数据处理主函数
 * @param  None
 * @retval None
 */
void Data_Handler_Process(void);

/**
 * @brief  处理坐标误差数据
 * @param  error_data: 坐标误差数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Process_Coord_Error(coord_error_data_t *error_data);

/**
 * @brief  处理舵机位置数据
 * @param  servo_data: 舵机位置数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Process_Servo_Position(servo_position_t *servo_data);

/**
 * @brief  更新舵机位置
 * @param  x_pos: X轴位置
 * @param  y_pos: Y轴位置
 * @retval 更新结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Update_Servo_Position(uint16_t x_pos, uint16_t y_pos);

/**
 * @brief  添加轨迹点
 * @param  point: 点坐标指针
 * @retval 添加结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Add_Point(point_t *point);

/**
 * @brief  清除所有轨迹点
 * @param  None
 * @retval None
 */
void Data_Handler_Clear_Points(void);

/**
 * @brief  开始任务
 * @param  task_num: 任务号
 * @retval 开始结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Start_Task(uint8_t task_num);

/**
 * @brief  停止当前任务
 * @param  None
 * @retval None
 */
void Data_Handler_Stop_Task(void);

/**
 * @brief  获取当前任务状态
 * @param  None
 * @retval 任务状态
 */
uint8_t Data_Handler_Get_Task_State(void);

/**
 * @brief  设置控制模式
 * @param  mode: 控制模式
 * @retval None
 */
void Data_Handler_Set_Control_Mode(control_mode_t mode);

/**
 * @brief  获取控制模式
 * @param  None
 * @retval 控制模式
 */
control_mode_t Data_Handler_Get_Control_Mode(void);

/**
 * @brief  舵机回中位
 * @param  None
 * @retval None
 */
void Data_Handler_Servo_Return_Center(void);

/**
 * @brief  设置舵机中位
 * @param  center_x: X轴中位
 * @param  center_y: Y轴中位
 * @retval None
 */
void Data_Handler_Set_Servo_Center(uint16_t center_x, uint16_t center_y);

/**
 * @brief  获取舵机当前位置
 * @param  x_pos: X轴位置指针
 * @param  y_pos: Y轴位置指针
 * @retval None
 */
void Data_Handler_Get_Servo_Position(uint16_t *x_pos, uint16_t *y_pos);

/**
 * @brief  坐标变换处理
 * @param  input_x: 输入X坐标
 * @param  input_y: 输入Y坐标
 * @param  output_x: 输出X坐标指针
 * @param  output_y: 输出Y坐标指针
 * @retval 变换结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Coordinate_Transform(float input_x, float input_y, 
                                         float *output_x, float *output_y);

/**
 * @brief  数据滤波处理
 * @param  input: 输入数据
 * @param  output: 输出数据指针
 * @retval 滤波结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Filter_Data(float input, float *output);

/**
 * @brief  获取数据处理统计信息
 * @param  None
 * @retval 统计信息指针
 */
data_stats_t* Data_Handler_Get_Stats(void);

/**
 * @brief  重置数据处理模块
 * @param  None
 * @retval None
 */
void Data_Handler_Reset(void);

/**
 * @brief  数据有效性检查
 * @param  data: 数据指针
 * @param  data_type: 数据类型
 * @retval 检查结果 (0=有效, 其他=无效)
 */
uint8_t Data_Handler_Validate_Data(void *data, uint8_t data_type);

#ifdef __cplusplus
}
#endif

#endif /* __DATA_HANDLER_H */
