# UART通信专用移植项目

## 项目概述

本项目是从原运动控制系统中提取UART通信和数据处理部分的专用移植版本，去除FreeRTOS依赖，改为裸机实现。专注于与OpenMV的串口通信、数据解析和处理功能。

## 核心功能

- **UART通信**: 与OpenMV的串口通信协议
- **数据解析**: 完整的通信协议解析
- **坐标处理**: 坐标误差数据处理
- **命令响应**: 多种命令类型支持
- **状态管理**: 系统状态和任务管理

## 系统架构

```
UART_AI/
├── Core/                          # 核心应用代码
│   ├── Inc/                       # 头文件
│   │   ├── main.h                 # 主头文件
│   │   ├── uart_protocol.h        # UART协议定义
│   │   ├── data_handler.h         # 数据处理模块
│   │   ├── system_manager.h       # 系统管理模块
│   │   └── ...                    # 其他头文件
│   └── Src/                       # 源文件
│       ├── main.c                 # 主程序
│       ├── uart_protocol.c        # UART协议实现
│       ├── data_handler.c         # 数据处理实现
│       ├── system_manager.c       # 系统管理实现
│       └── ...                    # 其他源文件
├── Drivers/                       # 驱动文件
│   ├── STM32F4xx_HAL_Driver/      # HAL库
│   ├── CMSIS/                     # CMSIS库
│   └── BSP/                       # 板级支持包
├── MDK-ARM/                       # Keil工程文件
│   └── UART_AI.uvprojx            # Keil工程
├── UART_AI.ioc                    # CubeMX配置
└── 文档/                          # 项目文档
```

## 硬件配置

- **MCU**: STM32F407VETx
- **时钟**: 168MHz系统时钟
- **UART3**: 与OpenMV通信 (115200, 8N1)
- **DMA**: UART接收DMA支持
- **定时器**: 系统节拍定时器

## 通信协议

### 数据包格式
```
OpenMV → STM32: [0xA1][CMD][DATA...][0x1A]
STM32 → OpenMV: [TASK_NUM] 或 [0xA1][CMD][0x1A]
```

### 命令定义
- `0x12`: Y轴舵机位置命令
- `0x13`: X轴舵机位置命令
- `0x02`: 坐标误差数据
- `0x11`: 任务完成命令
- `0x33`: 应答命令
- `0x44`: 点位更新命令

## 快速开始

1. **打开CubeMX**: 使用`UART_AI.ioc`生成基础代码
2. **编译工程**: 打开`MDK-ARM/UART_AI.uvprojx`编译
3. **下载调试**: 下载到STM32开发板
4. **连接OpenMV**: 使用UART3连接OpenMV
5. **功能测试**: 验证通信和数据处理功能

## 性能特点

- **高效通信**: DMA+空闲中断机制
- **实时处理**: 裸机环境，响应更快
- **模块化设计**: 清晰的代码结构
- **易于扩展**: 标准化接口设计

## 兼容性

- 与原OpenMV代码100%兼容
- 通信协议完全不变
- 硬件连接保持一致
