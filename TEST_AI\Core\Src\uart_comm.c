/**
  ******************************************************************************
  * @file    uart_comm.c
  * @brief   UART通信和数据处理模块实现
  ******************************************************************************
  */

#include "uart_comm.h"
#include "control.h"
#include "oled.h"
#include <stdio.h>
#include <string.h>

/* 全局变量定义 */
volatile uint8_t uart_rx_flag = 0;          // UART接收完成标志
volatile uint8_t uart_data_ready = 0;       // 数据准备就绪标志
volatile uint16_t uart_rx_length = 0;       // 接收数据长度
uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE]; // 接收缓冲区
uint8_t uart_tx_buffer[UART_TX_BUFFER_SIZE]; // 发送缓冲区

/* 通信相关标志位 */
volatile uint8_t rec_flag = 0;              // 接收标志
volatile uint8_t rec_ok_flag = 0;           // 接收完成标志
volatile uint8_t rx_ack = 0;                // 接收应答标志
volatile uint8_t task = 0;                  // 当前任务号
volatile uint8_t point_state = 0;           // 点状态

/* 坐标误差变量 */
volatile float servo_hori_error = 0;        // 水平舵机误差
volatile float servo_vert_error = 0;        // 垂直舵机误差

/* 内部状态变量 */
static uart_state_t uart_state = UART_STATE_IDLE;

/**
 * @brief  UART通信模块初始化
 * @param  None
 * @retval None
 */
void UART_Comm_Init(void)
{
    /* 清除所有标志位 */
    uart_rx_flag = 0;
    uart_data_ready = 0;
    uart_rx_length = 0;
    rec_flag = 0;
    rec_ok_flag = 0;
    rx_ack = 0;
    task = 0;
    point_state = 0;
    servo_hori_error = 0;
    servo_vert_error = 0;
    
    /* 设置初始状态 */
    uart_state = UART_STATE_IDLE;
    
    /* 启用UART空闲中断 */
    __HAL_UART_ENABLE_IT(&huart3, UART_IT_IDLE);
    
    /* 启动DMA接收 */
    HAL_UART_Receive_DMA(&huart3, uart_rx_buffer, UART_RX_BUFFER_SIZE);
}

/**
 * @brief  UART数据接收处理
 * @param  None
 * @retval None
 */
void UART_Data_Process(void)
{
    if (uart_data_ready) {
        uart_data_ready = 0;
        uart_state = UART_STATE_PROCESSING;
        
        /* 解析数据包 */
        if (UART_Parse_Packet(uart_rx_buffer, uart_rx_length) == 0) {
            /* 解析成功 */
            uart_state = UART_STATE_IDLE;
        } else {
            /* 解析失败 */
            uart_state = UART_STATE_ERROR;
        }
    }
}

/**
 * @brief  解析接收到的数据包
 * @param  buffer: 数据缓冲区
 * @param  length: 数据长度
 * @retval 解析结果 (0=成功, 1=失败)
 */
uint8_t UART_Parse_Packet(uint8_t *buffer, uint16_t length)
{
    if (length < 1) return 1;  // 数据长度不足
    
    uint8_t cmd = buffer[0];
    
    /* 处理舵机位置命令 */
    if (cmd == CMD_SERVO_Y || cmd == CMD_SERVO_X) {
        if (length >= 3) {
            UART_Handle_Servo_Cmd(cmd, &buffer[1]);
            return 0;
        }
    }
    
    /* 处理控制命令 */
    if (cmd == FRAME_HEADER_A1 && length >= 3 && buffer[length-1] == FRAME_TAIL_1A) {
        UART_Handle_Control_Cmd(buffer[1], &buffer[2], length-3);
        return 0;
    }
    
    return 1;  // 未知命令
}

/**
 * @brief  处理舵机位置命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @retval None
 */
void UART_Handle_Servo_Cmd(uint8_t cmd, uint8_t *data)
{
    uint16_t servo_value = (data[0] << 8) | data[1];
    
    if (cmd == CMD_SERVO_Y) {
        SERVOY = servo_value;
        OLED_ShowString(0, 0, "SERVO_Y OK");
    } else if (cmd == CMD_SERVO_X) {
        SERVOX = servo_value;
        OLED_ShowString(0, 0, "SERVO_X OK");
    }
}

/**
 * @brief  处理控制命令
 * @param  cmd: 命令字
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval None
 */
void UART_Handle_Control_Cmd(uint8_t cmd, uint8_t *data, uint8_t length)
{
    char txt[40];
    
    switch (cmd) {
        case CMD_TASK_COMPLETE:
            if (task == 1) {
                extern uint16_t servo_midx, servo_midy;
                servo_midx = SERVOX;
                servo_midy = SERVOY;
            }
            rec_ok_flag = 1;
            break;
            
        case CMD_ACK:
            rx_ack = 1;
            break;
            
        case CMD_POINT_UPDATE:
            point_state++;
            sprintf(txt, "point_num%d", point_state);
            OLED_ShowString(0, 0, txt);
            break;
            
        case CMD_COORD_ERROR:
            if (length >= 4) {
                UART_Handle_Coord_Error(data);
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief  处理坐标误差数据
 * @param  data: 数据指针
 * @retval None
 */
void UART_Handle_Coord_Error(uint8_t *data)
{
    /* 解析水平误差 */
    if (data[0] == 1) {
        servo_hori_error = -(float)data[1];
    } else {
        servo_hori_error = (float)data[1];
    }
    
    /* 解析垂直误差 */
    if (data[2] == 1) {
        servo_vert_error = -(float)data[3];
    } else {
        servo_vert_error = (float)data[3];
    }
    
    /* 设置接收标志 */
    rec_flag = 1;
}

/**
 * @brief  发送任务号
 * @param  task_num: 任务号
 * @retval None
 */
void UART_Send_Task_Number(uint8_t task_num)
{
    char task_str[2];
    
    /* 根据任务号映射发送的字符 */
    if (task_num == 5 || task_num == 7 || task_num == 9) {
        sprintf(task_str, "3");
    } else if (task_num == 6 || task_num == 8 || task_num == 10) {
        sprintf(task_str, "4");
    } else {
        sprintf(task_str, "%d", task_num);
    }
    
    HAL_UART_Transmit(&huart3, (uint8_t*)task_str, 1, 100);
}

/**
 * @brief  发送应答信号
 * @param  None
 * @retval None
 */
void UART_Send_ACK(void)
{
    uint8_t ack_packet[] = {FRAME_HEADER_A1, CMD_ACK, FRAME_TAIL_1A};
    HAL_UART_Transmit(&huart3, ack_packet, sizeof(ack_packet), 100);
}

/**
 * @brief  发送任务完成信号
 * @param  None
 * @retval None
 */
void UART_Send_Task_Complete(void)
{
    uint8_t complete_packet[] = {FRAME_HEADER_A1, CMD_TASK_COMPLETE, FRAME_TAIL_1A};
    HAL_UART_Transmit(&huart3, complete_packet, sizeof(complete_packet), 100);
}

/**
 * @brief  UART空闲中断回调函数
 * @param  huart: UART句柄
 * @retval None
 */
void UART_IDLE_Callback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART3) {
        /* 停止DMA传输 */
        HAL_UART_DMAStop(huart);
        
        /* 清除空闲中断标志 */
        __HAL_UART_CLEAR_IDLEFLAG(huart);
        
        /* 计算接收数据长度 */
        uart_rx_length = UART_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart3_rx);
        
        /* 重新启动DMA接收 */
        HAL_UART_Receive_DMA(huart, uart_rx_buffer, UART_RX_BUFFER_SIZE);
        
        /* 设置数据就绪标志 */
        uart_data_ready = 1;
        uart_state = UART_STATE_RECEIVING;
    }
}

/**
 * @brief  获取通信状态
 * @param  None
 * @retval 通信状态
 */
uart_state_t UART_Get_State(void)
{
    return uart_state;
}

/**
 * @brief  重置通信状态
 * @param  None
 * @retval None
 */
void UART_Reset_State(void)
{
    uart_state = UART_STATE_IDLE;
    uart_rx_flag = 0;
    uart_data_ready = 0;
    uart_rx_length = 0;
}
