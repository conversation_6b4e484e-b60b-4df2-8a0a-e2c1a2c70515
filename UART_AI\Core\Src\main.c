/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body (UART通信专用版本)
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "uart_protocol.h"
#include "data_handler.h"
#include "system_manager.h"
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* 全局变量定义 */
volatile system_state_t g_system_state = SYS_STATE_INIT;
volatile uint32_t g_system_tick = 0;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief  系统初始化
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    /* 系统管理模块初始化 */
    System_Manager_Init();
    
    /* UART协议模块初始化 */
    UART_Protocol_Init();
    
    /* 数据处理模块初始化 */
    Data_Handler_Init();
    
    /* 设置系统状态为就绪 */
    g_system_state = SYS_STATE_READY;
    
    /* LED指示系统启动完成 */
    LED_On(1);
    HAL_Delay(100);
    LED_Off(1);
}

/**
 * @brief  系统主处理函数
 * @param  None
 * @retval None
 */
void System_Process(void)
{
    /* 系统管理处理 */
    System_Manager_Process();
    
    /* UART协议处理 */
    UART_Protocol_Process();
    
    /* 数据处理 */
    Data_Handler_Process();
}

/**
 * @brief  系统错误处理
 * @param  None
 * @retval None
 */
void System_Error_Handler(void)
{
    /* 设置系统状态为错误 */
    g_system_state = SYS_STATE_ERROR;
    
    /* LED指示错误状态 */
    while(1) {
        LED_Toggle(1);
        HAL_Delay(200);
    }
}

/**
 * @brief  LED控制函数
 * @param  led_num: LED编号 (1-3)
 * @retval None
 */
void LED_On(uint8_t led_num)
{
    switch(led_num) {
        case 1:
            HAL_GPIO_WritePin(LED1_GPIO_Port, LED1_Pin, GPIO_PIN_RESET);
            break;
        case 2:
            HAL_GPIO_WritePin(LED2_GPIO_Port, LED2_Pin, GPIO_PIN_RESET);
            break;
        case 3:
            HAL_GPIO_WritePin(LED3_GPIO_Port, LED3_Pin, GPIO_PIN_RESET);
            break;
        default:
            break;
    }
}

void LED_Off(uint8_t led_num)
{
    switch(led_num) {
        case 1:
            HAL_GPIO_WritePin(LED1_GPIO_Port, LED1_Pin, GPIO_PIN_SET);
            break;
        case 2:
            HAL_GPIO_WritePin(LED2_GPIO_Port, LED2_Pin, GPIO_PIN_SET);
            break;
        case 3:
            HAL_GPIO_WritePin(LED3_GPIO_Port, LED3_Pin, GPIO_PIN_SET);
            break;
        default:
            break;
    }
}

void LED_Toggle(uint8_t led_num)
{
    switch(led_num) {
        case 1:
            HAL_GPIO_TogglePin(LED1_GPIO_Port, LED1_Pin);
            break;
        case 2:
            HAL_GPIO_TogglePin(LED2_GPIO_Port, LED2_Pin);
            break;
        case 3:
            HAL_GPIO_TogglePin(LED3_GPIO_Port, LED3_Pin);
            break;
        default:
            break;
    }
}

/**
 * @brief  延时函数
 * @param  ms: 延时时间(毫秒)
 * @retval None
 */
void Delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

void Delay_us(uint32_t us)
{
    uint32_t start = DWT->CYCCNT;
    uint32_t cycles = us * (SystemCoreClock / 1000000);
    while((DWT->CYCCNT - start) < cycles);
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_TIM6_Init();
  MX_USART3_UART_Init();

  /* USER CODE BEGIN 2 */
  
  /* 启动定时器 */
  HAL_TIM_Base_Start_IT(&htim6);
  
  /* 系统初始化 */
  System_Init();
  
  /* 设置系统状态为运行 */
  g_system_state = SYS_STATE_RUNNING;
  
  /* 启动信息输出 */
  printf("UART_AI System Started\r\n");
  printf("System Clock: %ld MHz\r\n", SystemCoreClock / 1000000);
  printf("UART Baudrate: %d\r\n", UART_BAUDRATE);
  
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    
    /* 系统主处理 */
    System_Process();
    
    /* 系统节拍更新 */
    g_system_tick++;
    
    /* 短暂延时 */
    HAL_Delay(1);
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM1) {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */
  
  if (htim->Instance == TIM6) {
    /* 10ms定时器中断 */
    static uint32_t heartbeat_counter = 0;
    
    /* 心跳计数 */
    heartbeat_counter++;
    if (heartbeat_counter >= 100) {  // 1秒心跳
      heartbeat_counter = 0;
      LED_Toggle(2);  // LED2作为心跳指示
    }
    
    /* 系统管理器心跳 */
    System_Manager_Heartbeat();
  }
  
  /* USER CODE END Callback 1 */
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  System_Error_Handler();
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
