/**
  ******************************************************************************
  * @file    key_handler.h
  * @brief   按键处理模块头文件
  ******************************************************************************
  */

#ifndef __KEY_HANDLER_H
#define __KEY_HANDLER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "key.h"

/* 按键功能定义 */
#define KEY_FUNC_RETURN_MIDDLE    1    // 按键1：回到中位
#define KEY_FUNC_PAUSE_TOGGLE     2    // 按键2：暂停/继续
#define KEY_FUNC_SPECIAL          3    // 按键3：特殊功能
#define KEY_FUNC_NEXT_TASK        4    // 按键4：下一个任务

/* 按键状态定义 */
typedef enum {
    KEY_STATE_RELEASED = 0,     // 按键释放
    KEY_STATE_PRESSED,          // 按键按下
    KEY_STATE_LONG_PRESSED      // 按键长按
} key_state_t;

/* 按键事件定义 */
typedef enum {
    KEY_EVENT_NONE = 0,         // 无事件
    KEY_EVENT_CLICK,            // 单击事件
    KEY_EVENT_LONG_CLICK,       // 长按事件
    KEY_EVENT_DOUBLE_CLICK      // 双击事件
} key_event_t;

/* 按键处理结构 */
typedef struct {
    uint8_t key_num;            // 按键号
    key_state_t state;          // 按键状态
    key_event_t event;          // 按键事件
    uint32_t press_time;        // 按下时间
    uint32_t release_time;      // 释放时间
    uint8_t click_count;        // 点击次数
} key_info_t;

/* 全局变量声明 */
extern volatile uint8_t key_scan_flag;    // 按键扫描标志

/* 函数声明 */

/**
 * @brief  按键处理模块初始化
 * @param  None
 * @retval None
 */
void KeyHandler_Init(void);

/**
 * @brief  按键扫描和处理任务
 * @param  None
 * @retval None
 */
void KeyHandler_Task(void);

/**
 * @brief  按键扫描处理
 * @param  None
 * @retval None
 */
void KeyHandler_Scan(void);

/**
 * @brief  处理按键1事件（回到中位）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key1_Process(key_event_t event);

/**
 * @brief  处理按键2事件（暂停/继续）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key2_Process(key_event_t event);

/**
 * @brief  处理按键3事件（特殊功能）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key3_Process(key_event_t event);

/**
 * @brief  处理按键4事件（下一个任务）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key4_Process(key_event_t event);

/**
 * @brief  获取按键状态
 * @param  key_num: 按键号
 * @retval 按键状态
 */
key_state_t KeyHandler_Get_State(uint8_t key_num);

/**
 * @brief  获取按键事件
 * @param  key_num: 按键号
 * @retval 按键事件
 */
key_event_t KeyHandler_Get_Event(uint8_t key_num);

/**
 * @brief  清除按键事件
 * @param  key_num: 按键号
 * @retval None
 */
void KeyHandler_Clear_Event(uint8_t key_num);

/**
 * @brief  按键防抖处理
 * @param  key_num: 按键号
 * @param  current_state: 当前状态
 * @retval 防抖后的状态
 */
uint8_t KeyHandler_Debounce(uint8_t key_num, uint8_t current_state);

/**
 * @brief  检测长按事件
 * @param  key_info: 按键信息指针
 * @retval None
 */
void KeyHandler_Check_Long_Press(key_info_t *key_info);

/**
 * @brief  检测双击事件
 * @param  key_info: 按键信息指针
 * @retval None
 */
void KeyHandler_Check_Double_Click(key_info_t *key_info);

#ifdef __cplusplus
}
#endif

#endif /* __KEY_HANDLER_H */
