/**
  ******************************************************************************
  * @file    key_handler.c
  * @brief   按键处理模块实现
  ******************************************************************************
  */

#include "key_handler.h"
#include "data_process.h"
#include "uart_comm.h"
#include "oled.h"

/* 按键配置参数 */
#define KEY_DEBOUNCE_TIME       20      // 防抖时间(ms)
#define KEY_LONG_PRESS_TIME     1000    // 长按时间(ms)
#define KEY_DOUBLE_CLICK_TIME   300     // 双击间隔时间(ms)

/* 全局变量定义 */
volatile uint8_t key_scan_flag = 0;     // 按键扫描标志

/* 按键信息数组 */
static key_info_t key_info[5] = {0};    // 支持4个按键，索引0不使用

/* 防抖缓冲区 */
static uint8_t key_debounce_buf[5][KEY_DEBOUNCE_TIME/10] = {0};
static uint8_t debounce_index[5] = {0};

/**
 * @brief  按键处理模块初始化
 * @param  None
 * @retval None
 */
void KeyHandler_Init(void)
{
    /* 初始化按键信息 */
    for (int i = 1; i <= 4; i++) {
        key_info[i].key_num = i;
        key_info[i].state = KEY_STATE_RELEASED;
        key_info[i].event = KEY_EVENT_NONE;
        key_info[i].press_time = 0;
        key_info[i].release_time = 0;
        key_info[i].click_count = 0;
    }
    
    /* 清除扫描标志 */
    key_scan_flag = 0;
}

/**
 * @brief  按键扫描和处理任务
 * @param  None
 * @retval None
 */
void KeyHandler_Task(void)
{
    if (key_scan_flag) {
        key_scan_flag = 0;
        KeyHandler_Scan();
    }
}

/**
 * @brief  按键扫描处理
 * @param  None
 * @retval None
 */
void KeyHandler_Scan(void)
{
    uint8_t key_value = Key_Scan();
    
    if (key_value != 0) {
        /* 处理按键事件 */
        switch (key_value) {
            case KEY_FUNC_RETURN_MIDDLE:
                KeyHandler_Key1_Process(KEY_EVENT_CLICK);
                break;
                
            case KEY_FUNC_PAUSE_TOGGLE:
                KeyHandler_Key2_Process(KEY_EVENT_CLICK);
                break;
                
            case KEY_FUNC_SPECIAL:
                KeyHandler_Key3_Process(KEY_EVENT_CLICK);
                break;
                
            case KEY_FUNC_NEXT_TASK:
                KeyHandler_Key4_Process(KEY_EVENT_CLICK);
                break;
                
            default:
                break;
        }
    }
}

/**
 * @brief  处理按键1事件（回到中位）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key1_Process(key_event_t event)
{
    if (event == KEY_EVENT_CLICK) {
        /* 舵机回到中位 */
        extern servo_control_t servo_ctrl;
        servo_ctrl.hori_all = servo_ctrl.midx;
        servo_ctrl.vert_all = servo_ctrl.midy;
        SERVOX = (uint16_t)servo_ctrl.hori_all;
        SERVOY = (uint16_t)servo_ctrl.vert_all;
        
        OLED_ShowString(0, 4, "Return Middle");
    }
}

/**
 * @brief  处理按键2事件（暂停/继续）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key2_Process(key_event_t event)
{
    if (event == KEY_EVENT_CLICK) {
        /* 切换暂停状态 */
        DataProcess_Toggle_Pause();
        
        extern system_status_t sys_status;
        if (sys_status.pause_flag) {
            OLED_ShowString(0, 4, "System Paused");
        } else {
            OLED_ShowString(0, 4, "System Running");
        }
    }
}

/**
 * @brief  处理按键3事件（特殊功能）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key3_Process(key_event_t event)
{
    if (event == KEY_EVENT_CLICK) {
        /* 处理特殊命令 */
        DataProcess_Handle_Special_Command();
        OLED_ShowString(0, 4, "Special Command");
    }
}

/**
 * @brief  处理按键4事件（下一个任务）
 * @param  event: 按键事件
 * @retval None
 */
void KeyHandler_Key4_Process(key_event_t event)
{
    if (event == KEY_EVENT_CLICK) {
        /* 切换到下一个任务 */
        DataProcess_Next_Task();
        
        extern system_status_t sys_status;
        char task_str[32];
        sprintf(task_str, "Switch to Task %d", sys_status.current_task);
        OLED_ShowString(0, 4, task_str);
    }
}

/**
 * @brief  获取按键状态
 * @param  key_num: 按键号
 * @retval 按键状态
 */
key_state_t KeyHandler_Get_State(uint8_t key_num)
{
    if (key_num >= 1 && key_num <= 4) {
        return key_info[key_num].state;
    }
    return KEY_STATE_RELEASED;
}

/**
 * @brief  获取按键事件
 * @param  key_num: 按键号
 * @retval 按键事件
 */
key_event_t KeyHandler_Get_Event(uint8_t key_num)
{
    if (key_num >= 1 && key_num <= 4) {
        return key_info[key_num].event;
    }
    return KEY_EVENT_NONE;
}

/**
 * @brief  清除按键事件
 * @param  key_num: 按键号
 * @retval None
 */
void KeyHandler_Clear_Event(uint8_t key_num)
{
    if (key_num >= 1 && key_num <= 4) {
        key_info[key_num].event = KEY_EVENT_NONE;
    }
}

/**
 * @brief  按键防抖处理
 * @param  key_num: 按键号
 * @param  current_state: 当前状态
 * @retval 防抖后的状态
 */
uint8_t KeyHandler_Debounce(uint8_t key_num, uint8_t current_state)
{
    if (key_num < 1 || key_num > 4) return 0;
    
    /* 将当前状态存入缓冲区 */
    key_debounce_buf[key_num][debounce_index[key_num]] = current_state;
    debounce_index[key_num] = (debounce_index[key_num] + 1) % (KEY_DEBOUNCE_TIME/10);
    
    /* 检查缓冲区中的所有值是否一致 */
    uint8_t stable_state = key_debounce_buf[key_num][0];
    for (int i = 1; i < KEY_DEBOUNCE_TIME/10; i++) {
        if (key_debounce_buf[key_num][i] != stable_state) {
            return 0;  // 状态不稳定
        }
    }
    
    return stable_state;
}

/**
 * @brief  检测长按事件
 * @param  key_info: 按键信息指针
 * @retval None
 */
void KeyHandler_Check_Long_Press(key_info_t *key_info)
{
    if (key_info->state == KEY_STATE_PRESSED) {
        uint32_t press_duration = HAL_GetTick() - key_info->press_time;
        if (press_duration >= KEY_LONG_PRESS_TIME) {
            key_info->state = KEY_STATE_LONG_PRESSED;
            key_info->event = KEY_EVENT_LONG_CLICK;
        }
    }
}

/**
 * @brief  检测双击事件
 * @param  key_info: 按键信息指针
 * @retval None
 */
void KeyHandler_Check_Double_Click(key_info_t *key_info)
{
    uint32_t current_time = HAL_GetTick();
    
    if (key_info->state == KEY_STATE_RELEASED && key_info->click_count == 1) {
        if (current_time - key_info->release_time >= KEY_DOUBLE_CLICK_TIME) {
            /* 超时，确认为单击 */
            key_info->event = KEY_EVENT_CLICK;
            key_info->click_count = 0;
        }
    } else if (key_info->click_count == 2) {
        /* 双击事件 */
        key_info->event = KEY_EVENT_DOUBLE_CLICK;
        key_info->click_count = 0;
    }
}
