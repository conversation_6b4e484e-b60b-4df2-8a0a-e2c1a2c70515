@echo off
echo ========================================
echo 运动控制系统移植 - 文件复制脚本
echo ========================================
echo.

REM 设置源路径和目标路径
set SOURCE_PATH=..\red
set TARGET_PATH=.

echo 正在复制BSP驱动文件...
if not exist "%TARGET_PATH%\Drivers\BSP" mkdir "%TARGET_PATH%\Drivers\BSP"

copy "%SOURCE_PATH%\Drivers\BSP\control.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\control.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\pid.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\pid.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\motor.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\motor.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\encoder.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\encoder.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\mpu6050.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\mpu6050.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\oled.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\oled.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\oledfont.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\key.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\key.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\bsp_uart.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\bsp_uart.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\24l01.c" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\24l01.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\common.h" "%TARGET_PATH%\Drivers\BSP\"
copy "%SOURCE_PATH%\Drivers\BSP\delay.h" "%TARGET_PATH%\Drivers\BSP\"

echo BSP驱动文件复制完成！
echo.

echo 正在复制HAL库文件...
if not exist "%TARGET_PATH%\Drivers\STM32F4xx_HAL_Driver" mkdir "%TARGET_PATH%\Drivers\STM32F4xx_HAL_Driver"
xcopy "%SOURCE_PATH%\Drivers\STM32F4xx_HAL_Driver" "%TARGET_PATH%\Drivers\STM32F4xx_HAL_Driver" /E /I /Y

echo HAL库文件复制完成！
echo.

echo 正在复制CMSIS文件...
if not exist "%TARGET_PATH%\Drivers\CMSIS" mkdir "%TARGET_PATH%\Drivers\CMSIS"
xcopy "%SOURCE_PATH%\Drivers\CMSIS" "%TARGET_PATH%\Drivers\CMSIS" /E /I /Y

echo CMSIS文件复制完成！
echo.

echo 正在复制启动文件...
if not exist "%TARGET_PATH%\MDK-ARM" mkdir "%TARGET_PATH%\MDK-ARM"
copy "%SOURCE_PATH%\MDK-ARM\startup_stm32f407xx.s" "%TARGET_PATH%\MDK-ARM\"

echo 启动文件复制完成！
echo.

echo 正在复制OpenMV代码...
if not exist "%TARGET_PATH%\openMV" mkdir "%TARGET_PATH%\openMV"
copy "%SOURCE_PATH%\openMV\red.py" "%TARGET_PATH%\openMV\"

echo OpenMV代码复制完成！
echo.

echo ========================================
echo 文件复制完成！
echo ========================================
echo.
echo 接下来请按照以下步骤操作：
echo 1. 使用STM32CubeMX打开 TEST_AI.ioc 文件
echo 2. 生成代码（选择MDK-ARM V5）
echo 3. 用提供的文件替换生成的main.c和stm32f4xx_it.c
echo 4. 添加system_flags.c和system_flags.h文件
echo 5. 打开Keil工程进行编译
echo.
echo 详细步骤请参考"移植步骤详解.md"文件
echo.
pause
