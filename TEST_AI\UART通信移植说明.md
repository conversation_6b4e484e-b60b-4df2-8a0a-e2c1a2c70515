# UART通信和数据处理移植说明

## 移植概述

本次移植专门针对原工程中的UART通信和数据处理部分，将其从FreeRTOS环境移植到裸机环境，保持所有通信协议和数据处理逻辑不变。

## 核心模块架构

### 1. UART通信模块 (`uart_comm.c/h`)
负责与OpenMV的串口通信，包括：
- 数据包接收和解析
- 通信协议处理
- 命令响应和发送

### 2. 数据处理模块 (`data_process.c/h`)
负责坐标数据处理和控制逻辑，包括：
- PID控制算法
- 舵机位置控制
- 任务状态管理

### 3. 按键处理模块 (`key_handler.c/h`)
负责按键扫描和功能处理，包括：
- 按键防抖
- 功能映射
- 事件处理

## 通信协议保持不变

### 数据包格式
```
OpenMV → STM32:
[0xA1][CMD][DATA...][0x1A]

STM32 → OpenMV:
[TASK_NUM] 或 [0xA1][CMD][0x1A]
```

### 命令字定义
- `0x12`: Y轴舵机位置命令
- `0x13`: X轴舵机位置命令
- `0x02`: 坐标误差数据
- `0x11`: 任务完成命令
- `0x33`: 应答命令
- `0x44`: 点位更新命令

### 坐标误差数据格式
```
[方向标志X][误差值X][方向标志Y][误差值Y]
方向标志: 0=正方向, 1=负方向
误差值: 0-255像素
```

## 核心功能实现

### 1. UART接收处理
```c
void UART_IDLE_Callback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART3) {
        // 停止DMA，计算接收长度
        HAL_UART_DMAStop(huart);
        uart_rx_length = UART_RX_BUFFER_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart3_rx);
        
        // 重启DMA接收
        HAL_UART_Receive_DMA(huart, uart_rx_buffer, UART_RX_BUFFER_SIZE);
        
        // 设置数据就绪标志
        uart_data_ready = 1;
    }
}
```

### 2. 数据包解析
```c
uint8_t UART_Parse_Packet(uint8_t *buffer, uint16_t length)
{
    uint8_t cmd = buffer[0];
    
    // 舵机位置命令
    if (cmd == CMD_SERVO_Y || cmd == CMD_SERVO_X) {
        UART_Handle_Servo_Cmd(cmd, &buffer[1]);
        return 0;
    }
    
    // 控制命令
    if (cmd == FRAME_HEADER_A1 && buffer[length-1] == FRAME_TAIL_1A) {
        UART_Handle_Control_Cmd(buffer[1], &buffer[2], length-3);
        return 0;
    }
    
    return 1;  // 未知命令
}
```

### 3. 坐标误差处理
```c
void UART_Handle_Coord_Error(uint8_t *data)
{
    // 解析水平误差
    if (data[0] == 1) {
        servo_hori_error = -(float)data[1];
    } else {
        servo_hori_error = (float)data[1];
    }
    
    // 解析垂直误差
    if (data[2] == 1) {
        servo_vert_error = -(float)data[3];
    } else {
        servo_vert_error = (float)data[3];
    }
    
    rec_flag = 1;  // 设置接收标志
}
```

### 4. PID控制处理
```c
void Task1_Control_Process(void)
{
    if (sys_status.current_task == 1 && rec_ok_flag == 0) {
        // 发送任务号
        if (rx_ack == 0) {
            UART_Send_Task_Number(1);
        }
        
        // PID控制
        if (rec_flag == 1) {
            rec_flag = 0;
            
            float hori_delta = PID_SERVO(&pid_POINTX, -servo_hori_error);
            float vert_delta = PID_SERVO(&pid_POINTY, -servo_vert_error);
            
            Servo_Update_Position(hori_delta, vert_delta);
        }
    }
}
```

## 任务调度机制

### 原FreeRTOS任务
```c
// 原来的多任务结构
StartUart1Task()    // UART处理任务
StartServoTask()    // 舵机控制任务
StartKeyTask()      // 按键扫描任务
```

### 移植后的轮询结构
```c
// 主循环中的任务调度
void main_loop(void)
{
    while(1) {
        DataProcess_Task();    // 数据处理任务
        KeyHandler_Task();     // 按键处理任务
        HAL_Delay(1);         // 1ms延时
    }
}
```

## 中断处理优化

### 定时器中断
```c
void TIM6_DAC_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&htim6);
    
    // 10ms定时器中断处理
    static uint8_t key_cnt = 0;
    if(++key_cnt >= 10) {  // 100ms扫描一次按键
        key_cnt = 0;
        key_scan_flag = 1;
    }
}
```

### UART中断
```c
void USART3_IRQHandler(void)
{
    HAL_UART_IRQHandler(&huart3);
    
    // UART空闲中断处理
    if (__HAL_UART_GET_FLAG(&huart3, UART_FLAG_IDLE) != RESET) {
        UART_IDLE_Callback(&huart3);
    }
}
```

## 按键功能映射

| 按键 | 功能 | 实现 |
|------|------|------|
| KEY1 | 回到中位 | `Servo_Return_Middle()` |
| KEY2 | 暂停/继续 | `DataProcess_Toggle_Pause()` |
| KEY3 | 特殊功能 | `DataProcess_Handle_Special_Command()` |
| KEY4 | 下一任务 | `DataProcess_Next_Task()` |

## 系统状态管理

### 控制模式
- `CONTROL_MODE_IDLE`: 空闲模式
- `CONTROL_MODE_POINT`: 点位控制模式（任务1）
- `CONTROL_MODE_TRACK`: 轨迹跟踪模式（任务2-10）
- `CONTROL_MODE_RESET`: 复位模式

### 任务状态
- `TASK_STATE_INIT`: 初始化状态
- `TASK_STATE_RUNNING`: 运行状态
- `TASK_STATE_COMPLETE`: 完成状态
- `TASK_STATE_ERROR`: 错误状态

## 编译配置

### Keil工程设置
1. **包含路径**:
   ```
   ../Core/Inc
   ../Drivers/STM32F4xx_HAL_Driver/Inc
   ../Drivers/CMSIS/Device/ST/STM32F4xx/Include
   ../Drivers/CMSIS/Include
   ../Drivers/BSP
   ```

2. **宏定义**:
   ```
   USE_HAL_DRIVER
   STM32F407xx
   ```

3. **源文件组织**:
   - Application/User/Core: 核心应用文件
   - Drivers/BSP: BSP驱动文件
   - Drivers/HAL: HAL库文件

## 性能优势

### 1. 响应时间优化
- 去除任务切换开销
- 直接中断处理
- 更快的数据响应

### 2. 内存使用优化
- 减少任务栈空间
- 简化数据结构
- 更高效的内存利用

### 3. 代码可维护性
- 清晰的模块划分
- 简单的调用关系
- 易于调试和扩展

## 使用说明

### 1. 初始化
```c
UART_Comm_Init();      // 初始化UART通信
DataProcess_Init();    // 初始化数据处理
KeyHandler_Init();     // 初始化按键处理
```

### 2. 主循环
```c
while(1) {
    DataProcess_Task();    // 处理数据和控制
    KeyHandler_Task();     // 处理按键事件
    HAL_Delay(1);         // 系统延时
}
```

### 3. 功能验证
- 串口通信正常
- 坐标数据解析正确
- PID控制响应及时
- 按键功能正常

## 注意事项

1. **保持协议兼容**: 与OpenMV的通信协议完全不变
2. **实时性要求**: 主循环不能有长时间阻塞
3. **中断优先级**: 合理设置UART和定时器中断优先级
4. **内存管理**: 注意全局变量的使用和栈空间

## 总结

本次移植成功将UART通信和数据处理部分从FreeRTOS环境迁移到裸机环境，保持了所有原有功能，同时获得了更好的实时性能和更简洁的代码结构。移植后的系统具有更高的可维护性和扩展性。
