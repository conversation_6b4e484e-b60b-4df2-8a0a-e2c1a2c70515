/**
 ******************************************************************************
 * @file    data_handler.c
 * @brief   数据处理模块实现
 ******************************************************************************
 */

#include "data_handler.h"
#include "uart_protocol.h"
#include "system_manager.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

/* 全局变量定义 */
coord_error_t g_coord_error = {0};     // 当前坐标误差
servo_control_t g_servo_control = {0}; // 舵机控制
task_control_t g_task_control = {0};   // 任务控制
data_stats_t g_data_stats = {0};       // 数据统计

volatile uint8_t g_data_ready_flag = 0;                       // 数据就绪标志
volatile control_mode_t g_control_mode = CONTROL_MODE_MANUAL; // 控制模式

/* 内部变量 */
static float filter_buffer_x[5] = {0}; // X轴滤波缓冲区
static float filter_buffer_y[5] = {0}; // Y轴滤波缓冲区
static uint8_t filter_index = 0;       // 滤波索引

/**
 * @brief  数据处理模块初始化
 * @param  None
 * @retval None
 */
void Data_Handler_Init(void)
{
    /* 清除所有数据结构 */
    memset(&g_coord_error, 0, sizeof(coord_error_t));
    memset(&g_servo_control, 0, sizeof(servo_control_t));
    memset(&g_task_control, 0, sizeof(task_control_t));
    memset(&g_data_stats, 0, sizeof(data_stats_t));

    /* 初始化舵机控制参数 */
    g_servo_control.current_x = SERVO_CENTER_X;
    g_servo_control.current_y = SERVO_CENTER_Y;
    g_servo_control.target_x = SERVO_CENTER_X;
    g_servo_control.target_y = SERVO_CENTER_Y;
    g_servo_control.center_x = SERVO_CENTER_X;
    g_servo_control.center_y = SERVO_CENTER_Y;
    g_servo_control.enabled = 1;

    /* 初始化任务控制参数 */
    g_task_control.current_task = TASK_IDLE;
    g_task_control.task_state = DATA_STATE_IDLE;
    g_task_control.point_index = 0;
    g_task_control.total_points = 0;
    g_task_control.timeout = 30000; // 30秒超时

    /* 清除标志位 */
    g_data_ready_flag = 0;
    g_control_mode = CONTROL_MODE_MANUAL;

    /* 清除滤波缓冲区 */
    memset(filter_buffer_x, 0, sizeof(filter_buffer_x));
    memset(filter_buffer_y, 0, sizeof(filter_buffer_y));
    filter_index = 0;

    printf("Data Handler Initialized\r\n");
}

/**
 * @brief  数据处理主函数
 * @param  None
 * @retval None
 */
void Data_Handler_Process(void)
{
    /* 检查数据就绪标志 */
    if (g_data_ready_flag)
    {
        g_data_ready_flag = 0;

        /* 处理坐标误差数据 */
        if (g_coord_error.valid)
        {
            /* 数据滤波 */
            float filtered_x, filtered_y;
            Data_Handler_Filter_Data(g_coord_error.hori_error, &filtered_x);
            Data_Handler_Filter_Data(g_coord_error.vert_error, &filtered_y);

            /* 更新滤波后的误差 */
            g_coord_error.hori_error = filtered_x;
            g_coord_error.vert_error = filtered_y;

            /* 根据控制模式处理数据 */
            switch (g_control_mode)
            {
            case CONTROL_MODE_AUTO:
                /* 自动模式：直接应用误差修正 */
                Data_Handler_Apply_Error_Correction();
                break;

            case CONTROL_MODE_TRACK:
                /* 轨迹模式：按轨迹点控制 */
                Data_Handler_Track_Control();
                break;

            case CONTROL_MODE_MANUAL:
            default:
                /* 手动模式：仅记录数据 */
                break;
            }

            g_coord_error.valid = 0;
            g_data_stats.coord_errors_processed++;
        }
    }

    /* 任务超时检查 */
    if (g_task_control.task_state == DATA_STATE_PROCESSING)
    {
        uint32_t elapsed = HAL_GetTick() - g_task_control.start_time;
        if (elapsed > g_task_control.timeout)
        {
            printf("Task %d Timeout\r\n", g_task_control.current_task);
            Data_Handler_Stop_Task();
            System_Manager_Handle_Error(ERROR_UART_TIMEOUT);
        }
    }
}

/**
 * @brief  处理坐标误差数据
 * @param  error_data: 坐标误差数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Process_Coord_Error(coord_error_data_t *error_data)
{
    if (!error_data)
        return 1;

    /* 转换坐标误差数据 */
    g_coord_error.hori_error = error_data->hori_dir ? -(float)error_data->hori_value : (float)error_data->hori_value;

    g_coord_error.vert_error = error_data->vert_dir ? -(float)error_data->vert_value : (float)error_data->vert_value;

    g_coord_error.timestamp = HAL_GetTick();
    g_coord_error.valid = 1;

    /* 设置数据就绪标志 */
    g_data_ready_flag = 1;

    return 0;
}

/**
 * @brief  处理舵机位置数据
 * @param  servo_data: 舵机位置数据指针
 * @retval 处理结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Process_Servo_Position(servo_position_t *servo_data)
{
    if (!servo_data)
        return 1;

    /* 更新舵机位置 */
    if (servo_data->servo_x_pos != 0)
    {
        g_servo_control.target_x = servo_data->servo_x_pos;
        g_servo_control.current_x = servo_data->servo_x_pos;
    }

    if (servo_data->servo_y_pos != 0)
    {
        g_servo_control.target_y = servo_data->servo_y_pos;
        g_servo_control.current_y = servo_data->servo_y_pos;
    }

    g_data_stats.servo_commands_processed++;

    printf("Servo Position Updated: X=%d, Y=%d\r\n",
           g_servo_control.current_x, g_servo_control.current_y);

    return 0;
}

/**
 * @brief  更新舵机位置
 * @param  x_pos: X轴位置
 * @param  y_pos: Y轴位置
 * @retval 更新结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Update_Servo_Position(uint16_t x_pos, uint16_t y_pos)
{
    /* 位置范围检查 */
    if (x_pos < MIN_SERVO_POSITION || x_pos > MAX_SERVO_POSITION ||
        y_pos < MIN_SERVO_POSITION || y_pos > MAX_SERVO_POSITION)
    {
        return 1; // 位置超出范围
    }

    g_servo_control.target_x = x_pos;
    g_servo_control.target_y = y_pos;
    g_servo_control.current_x = x_pos;
    g_servo_control.current_y = y_pos;

    return 0;
}

/**
 * @brief  添加轨迹点
 * @param  point: 点坐标指针
 * @retval 添加结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Add_Point(point_t *point)
{
    if (!point || g_task_control.total_points >= MAX_POINTS)
    {
        return 1; // 参数错误或点数超限
    }

    g_task_control.points[g_task_control.total_points] = *point;
    g_task_control.points[g_task_control.total_points].valid = 1;
    g_task_control.total_points++;

    printf("Point Added: (%.2f, %.2f), Total: %d\r\n",
           point->x, point->y, g_task_control.total_points);

    return 0;
}

/**
 * @brief  清除所有轨迹点
 * @param  None
 * @retval None
 */
void Data_Handler_Clear_Points(void)
{
    memset(g_task_control.points, 0, sizeof(g_task_control.points));
    g_task_control.total_points = 0;
    g_task_control.point_index = 0;

    printf("All Points Cleared\r\n");
}

/**
 * @brief  开始任务
 * @param  task_num: 任务号
 * @retval 开始结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Start_Task(uint8_t task_num)
{
    if (task_num > MAX_TASKS)
    {
        return 1; // 任务号超出范围
    }

    g_task_control.current_task = task_num;
    g_task_control.task_state = DATA_STATE_PROCESSING;
    g_task_control.start_time = HAL_GetTick();
    g_task_control.point_index = 0;

    /* 根据任务类型设置控制模式 */
    switch (task_num)
    {
    case TASK_POINT_CONTROL:
        g_control_mode = CONTROL_MODE_AUTO;
        break;
    case TASK_TRACK_CONTROL:
    case TASK_RECT_TRACK:
    case TASK_CIRCLE_TRACK:
        g_control_mode = CONTROL_MODE_TRACK;
        break;
    default:
        g_control_mode = CONTROL_MODE_MANUAL;
        break;
    }

    printf("Task %d Started, Mode: %d\r\n", task_num, g_control_mode);

    return 0;
}

/**
 * @brief  停止当前任务
 * @param  None
 * @retval None
 */
void Data_Handler_Stop_Task(void)
{
    printf("Task %d Stopped\r\n", g_task_control.current_task);

    g_task_control.current_task = TASK_IDLE;
    g_task_control.task_state = DATA_STATE_COMPLETE;
    g_control_mode = CONTROL_MODE_MANUAL;
    g_data_stats.tasks_completed++;

    /* 发送任务完成信号 */
    UART_Protocol_Send_Complete();
}

/**
 * @brief  应用误差修正
 * @param  None
 * @retval None
 */
void Data_Handler_Apply_Error_Correction(void)
{
    /* 简单的比例控制 */
    float gain_x = 2.0f; // X轴增益
    float gain_y = 2.0f; // Y轴增益

    /* 计算修正量 */
    int16_t correction_x = (int16_t)(g_coord_error.hori_error * gain_x);
    int16_t correction_y = (int16_t)(g_coord_error.vert_error * gain_y);

    /* 应用修正 */
    uint16_t new_x = g_servo_control.current_x + correction_x;
    uint16_t new_y = g_servo_control.current_y + correction_y;

    /* 范围限制 */
    if (new_x < MIN_SERVO_POSITION)
        new_x = MIN_SERVO_POSITION;
    if (new_x > MAX_SERVO_POSITION)
        new_x = MAX_SERVO_POSITION;
    if (new_y < MIN_SERVO_POSITION)
        new_y = MIN_SERVO_POSITION;
    if (new_y > MAX_SERVO_POSITION)
        new_y = MAX_SERVO_POSITION;

    /* 更新舵机位置 */
    Data_Handler_Update_Servo_Position(new_x, new_y);
}

/**
 * @brief  轨迹控制处理
 * @param  None
 * @retval None
 */
void Data_Handler_Track_Control(void)
{
    if (g_task_control.point_index >= g_task_control.total_points)
    {
        /* 轨迹完成 */
        Data_Handler_Stop_Task();
        return;
    }

    /* 获取当前目标点 */
    point_t *target = &g_task_control.points[g_task_control.point_index];

    /* 检查是否到达目标点 */
    float distance = sqrtf(g_coord_error.hori_error * g_coord_error.hori_error +
                           g_coord_error.vert_error * g_coord_error.vert_error);

    if (distance < 2.0f)
    { // 2像素精度
        /* 到达目标点，切换到下一个点 */
        g_task_control.point_index++;
        printf("Reached Point %d, Next: %d\r\n",
               g_task_control.point_index - 1, g_task_control.point_index);
    }
    else
    {
        /* 应用误差修正 */
        Data_Handler_Apply_Error_Correction();
    }
}

/**
 * @brief  获取当前任务状态
 * @param  None
 * @retval 任务状态
 */
uint8_t Data_Handler_Get_Task_State(void)
{
    return g_task_control.task_state;
}

/**
 * @brief  设置控制模式
 * @param  mode: 控制模式
 * @retval None
 */
void Data_Handler_Set_Control_Mode(control_mode_t mode)
{
    g_control_mode = mode;
    printf("Control Mode Set: %d\r\n", mode);
}

/**
 * @brief  获取控制模式
 * @param  None
 * @retval 控制模式
 */
control_mode_t Data_Handler_Get_Control_Mode(void)
{
    return g_control_mode;
}

/**
 * @brief  舵机回中位
 * @param  None
 * @retval None
 */
void Data_Handler_Servo_Return_Center(void)
{
    Data_Handler_Update_Servo_Position(g_servo_control.center_x,
                                       g_servo_control.center_y);
    printf("Servo Returned to Center: (%d, %d)\r\n",
           g_servo_control.center_x, g_servo_control.center_y);
}

/**
 * @brief  设置舵机中位
 * @param  center_x: X轴中位
 * @param  center_y: Y轴中位
 * @retval None
 */
void Data_Handler_Set_Servo_Center(uint16_t center_x, uint16_t center_y)
{
    g_servo_control.center_x = center_x;
    g_servo_control.center_y = center_y;
    printf("Servo Center Set: (%d, %d)\r\n", center_x, center_y);
}

/**
 * @brief  获取舵机当前位置
 * @param  x_pos: X轴位置指针
 * @param  y_pos: Y轴位置指针
 * @retval None
 */
void Data_Handler_Get_Servo_Position(uint16_t *x_pos, uint16_t *y_pos)
{
    if (x_pos)
        *x_pos = g_servo_control.current_x;
    if (y_pos)
        *y_pos = g_servo_control.current_y;
}

/**
 * @brief  坐标变换处理
 * @param  input_x: 输入X坐标
 * @param  input_y: 输入Y坐标
 * @param  output_x: 输出X坐标指针
 * @param  output_y: 输出Y坐标指针
 * @retval 变换结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Coordinate_Transform(float input_x, float input_y,
                                          float *output_x, float *output_y)
{
    if (!output_x || !output_y)
        return 1;

    /* 简单的线性变换 */
    *output_x = input_x * 1.0f; // 可以添加缩放和偏移
    *output_y = input_y * 1.0f;

    return 0;
}

/**
 * @brief  数据滤波处理
 * @param  input: 输入数据
 * @param  output: 输出数据指针
 * @retval 滤波结果 (0=成功, 其他=错误码)
 */
uint8_t Data_Handler_Filter_Data(float input, float *output)
{
    if (!output)
        return 1;

    /* 移动平均滤波 */
    static uint8_t init_flag = 0;

    if (!init_flag)
    {
        /* 初始化滤波缓冲区 */
        for (int i = 0; i < 5; i++)
        {
            filter_buffer_x[i] = input;
        }
        init_flag = 1;
    }

    /* 更新滤波缓冲区 */
    filter_buffer_x[filter_index] = input;
    filter_index = (filter_index + 1) % 5;

    /* 计算平均值 */
    float sum = 0;
    for (int i = 0; i < 5; i++)
    {
        sum += filter_buffer_x[i];
    }

    *output = sum / 5.0f;

    return 0;
}

/**
 * @brief  获取数据处理统计信息
 * @param  None
 * @retval 统计信息指针
 */
data_stats_t *Data_Handler_Get_Stats(void)
{
    g_data_stats.total_processed = g_data_stats.coord_errors_processed +
                                   g_data_stats.servo_commands_processed;
    return &g_data_stats;
}

/**
 * @brief  重置数据处理模块
 * @param  None
 * @retval None
 */
void Data_Handler_Reset(void)
{
    Data_Handler_Init();
    printf("Data Handler Reset\r\n");
}

/**
 * @brief  数据有效性检查
 * @param  data: 数据指针
 * @param  data_type: 数据类型
 * @retval 检查结果 (0=有效, 其他=无效)
 */
uint8_t Data_Handler_Validate_Data(void *data, uint8_t data_type)
{
    if (!data)
        return 1;

    switch (data_type)
    {
    case 1: // 坐标误差数据
    {
        coord_error_data_t *error_data = (coord_error_data_t *)data;
        if (error_data->hori_value > MAX_COORD_ERROR ||
            error_data->vert_value > MAX_COORD_ERROR)
        {
            return 2; // 数值超出范围
        }
        break;
    }
    case 2: // 舵机位置数据
    {
        servo_position_t *servo_data = (servo_position_t *)data;
        if (servo_data->servo_x_pos > MAX_SERVO_POSITION ||
            servo_data->servo_y_pos > MAX_SERVO_POSITION ||
            servo_data->servo_x_pos < MIN_SERVO_POSITION ||
            servo_data->servo_y_pos < MIN_SERVO_POSITION)
        {
            return 3; // 位置超出范围
        }
        break;
    }
    default:
        return 4; // 未知数据类型
    }

    return 0; // 数据有效
}
