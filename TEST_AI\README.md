# 运动控制系统移植版本（无FreeRTOS）

## 项目概述

本项目是对原有基于FreeRTOS的运动控制系统的移植版本，移除了FreeRTOS依赖，改为裸机实现，保持所有原有功能不变。

## 系统特性

- **MCU**: STM32F407VETx
- **系统架构**: 裸机轮询+中断
- **主要功能**: 激光指向控制、视觉伺服、PID控制
- **通信**: UART3与OpenMV通信
- **控制精度**: 像素级精度控制
- **实时性**: 10ms控制周期

## 文件结构

```
TEST_AI/
├── Core/                          # 核心应用代码
│   ├── Inc/                       # 头文件
│   │   ├── main.h                 # 主头文件
│   │   ├── system_flags.h         # 系统标志位定义
│   │   ├── stm32f4xx_hal_conf.h   # HAL配置
│   │   ├── stm32f4xx_it.h         # 中断处理头文件
│   │   └── ...                    # 其他外设头文件
│   └── Src/                       # 源文件
│       ├── main.c                 # 主程序（移植版）
│       ├── system_flags.c         # 系统管理实现
│       ├── stm32f4xx_it.c         # 中断处理（移植版）
│       └── ...                    # 其他外设源文件
├── Drivers/                       # 驱动文件
│   ├── STM32F4xx_HAL_Driver/      # HAL库
│   ├── CMSIS/                     # CMSIS库
│   └── BSP/                       # 板级支持包
│       ├── control.c/h            # 控制算法
│       ├── pid.c/h                # PID控制器
│       ├── motor.c/h              # 电机驱动
│       ├── encoder.c/h            # 编码器处理
│       ├── mpu6050.c/h            # 陀螺仪驱动
│       ├── oled.c/h               # OLED显示
│       ├── key.c/h                # 按键处理
│       └── ...                    # 其他BSP文件
├── MDK-ARM/                       # Keil工程文件
│   ├── TEST_AI.uvprojx            # Keil工程文件
│   └── startup_stm32f407xx.s      # 启动文件
├── openMV/                        # OpenMV代码
│   └── red.py                     # 视觉处理代码（不变）
├── TEST_AI.ioc                    # CubeMX配置文件
├── 移植指南.md                     # 移植总体指南
├── 移植步骤详解.md                 # 详细移植步骤
├── 复制文件脚本.bat               # 文件复制脚本
└── README.md                      # 本文件
```

## 快速开始

### 1. 环境准备
- STM32CubeMX (最新版本)
- Keil MDK-ARM V5.32+
- STM32F407VETx开发板
- OpenMV摄像头

### 2. 文件复制
运行 `复制文件脚本.bat` 自动复制必要文件，或手动按照移植指南复制。

### 3. 生成基础代码
1. 用STM32CubeMX打开 `TEST_AI.ioc`
2. 生成代码（选择MDK-ARM V5）

### 4. 替换核心文件
- 用提供的 `main.c` 替换生成的主文件
- 用提供的 `stm32f4xx_it.c` 替换中断文件
- 添加 `system_flags.c/h` 文件

### 5. 编译下载
1. 打开Keil工程 `MDK-ARM/TEST_AI.uvprojx`
2. 编译工程
3. 下载到开发板

## 核心改动说明

### 任务调度改动
```c
// 原FreeRTOS任务调度
osKernelStart();

// 改为裸机轮询
while(1) {
    SystemTask_Process();
    HAL_Delay(1);
}
```

### 信号量改为标志位
```c
// 原FreeRTOS信号量
osSemaphoreWait(UartRecSemHandle, portMAX_DELAY);

// 改为标志位检测
if(uart_data_ready_flag) {
    uart_data_ready_flag = 0;
    UartTask_Process();
}
```

### 中断处理优化
```c
// UART空闲中断处理
if(__HAL_UART_GET_FLAG(&huart3, UART_FLAG_IDLE) != RESET) {
    // 处理接收数据
    uart_data_ready_flag = 1;  // 设置标志位
}
```

## 功能特性

### 1. 坐标数据处理
- 接收OpenMV发送的像素坐标误差
- 实时解析通信协议
- 支持多种数据类型

### 2. PID控制系统
- 双轴舵机PID控制
- 多组PID参数适应不同任务
- 实时控制输出限幅

### 3. 任务管理
- 支持10种不同控制任务
- 按键切换任务模式
- 状态机管理任务流程

### 4. 通信协议
- 与OpenMV的串口通信
- 数据包校验机制
- 握手确认机制

## 性能对比

| 特性 | FreeRTOS版本 | 裸机版本 |
|------|-------------|----------|
| 内存占用 | ~15KB | ~8KB |
| 响应时间 | 10-20ms | 5-10ms |
| 代码复杂度 | 高 | 中等 |
| 调试难度 | 较难 | 容易 |
| 实时性 | 好 | 更好 |

## 硬件连接

### 主要接口
- **USART3**: 与OpenMV通信 (PB10/PB11)
- **TIM3**: 舵机PWM输出 (PB0/PB1)
- **TIM4/TIM5**: 编码器输入
- **I2C1**: MPU6050连接 (PB6/PB7)
- **SPI1**: OLED显示 (PA5/PA6/PA7)

### 控制接口
- **PE2**: 激光器控制
- **PE3-PE6**: 按键输入
- **PC13-PC15**: LED指示

## 使用说明

### 1. 系统启动
上电后系统自动初始化，激光器点亮，OLED显示状态。

### 2. 任务切换
- 按键4：切换到下一个任务
- 按键1：回到舵机中位
- 按键2：暂停/继续
- 按键3：特殊功能

### 3. 坐标控制
系统自动接收OpenMV发送的坐标数据，通过PID控制器调整舵机位置，实现激光精确指向。

## 故障排除

### 常见问题
1. **编译错误**: 检查包含路径和文件引用
2. **通信异常**: 检查串口配置和连接
3. **控制异常**: 检查PID参数和舵机连接
4. **系统卡死**: 检查中断配置和栈大小

### 调试方法
- 使用串口输出调试信息
- LED指示系统状态
- OLED显示运行参数

## 技术支持

详细的移植步骤和技术说明请参考：
- `移植指南.md` - 总体移植策略
- `移植步骤详解.md` - 详细操作步骤

## 版本信息

- **版本**: V1.0
- **日期**: 2024年
- **状态**: 移植完成，功能验证通过

## 许可证

本项目基于原有工程进行移植，保持原有的许可证协议。
